# QR码翻译服务自动检测和添加功能 - 实际使用指南

## 功能概述

当处理QR码子项目时，如果发现服务标题在 `business-translation-tool.md` 中没有对应的翻译数据，系统将自动：

1. **识别新服务类型**
2. **添加标准化目录结构**
3. **创建10语言翻译模板**
4. **提醒用户补充内容**

## 使用场景

### 场景1：发现完全新的服务类型

**示例**：处理项目时发现子项目 "Kuala Lumpur Heritage Walking Tour"

**系统行为**：
```
🔍 检查服务是否存在: Kuala Lumpur Heritage Walking Tour
❌ 未找到匹配的服务: Kuala Lumpur Heritage Walking Tour
🎯 识别服务类型: Kuala Lumpur Heritage Walking Tour → tour (关键词: tour)
🚀 开始添加新服务到翻译数据库: Kuala Lumpur Heritage Walking Tour
✅ 新服务添加完成: Kuala Lumpur Heritage Walking Tour
📝 目录编号: 10
🏷️ 服务类型: tour
```

**结果**：自动在 `business-translation-tool.md` 中添加：

```markdown
## 目录 (更新)
10. [Kuala Lumpur Heritage Walking Tour](#10-kuala-lumpur-heritage-walking-tour)

## 10. Kuala Lumpur Heritage Walking Tour
| 语言 (Language) | Description | Remark |
| :--- | :--- | :--- |
| Bahasa Indonesia | [待补充] | [待补充] |
| Bahasa Melayu | [待补充] | [待补充] |
| English | [待补充] | [待补充] |
| Tiếng Việt | [待补充] | [待补充] |
| Русский | [待补充] | [待补充] |
| ภาษาไทย | [待补充] | [待补充] |
| 日本語 | [待补充] | [待补充] |
| 简体中文 | [待补充] | [待补充] |
| 繁體中文 | [待补充] | [待补充] |
| 한국어 | [待补充] | [待补充] |
```

### 场景2：发现相似但不完全匹配的服务

**示例**：处理项目时发现子项目 "KLIA2 => Kuala Lumpur"

**系统行为**：
```
🔍 检查服务是否存在: KLIA2 => Kuala Lumpur
🔄 找到相似服务: KLIA2 => Kuala Lumpur ≈ KLIA => Kuala Lumpur
✅ 找到精确匹配: KLIA2 => Kuala Lumpur (相似度匹配)
```

**结果**：使用现有的 "KLIA => Kuala Lumpur" 翻译数据，无需添加新条目。

## 实际集成到翻译流程

### 第一步：翻译状态识别（增强版）

在原有的五步骤流程第一步中，增加服务检测：

```javascript
// 1. 获取子项目数据
const subProjects = [
    { sub_id: '408', title: 'Point to Point Transfer Service (Genting)' },
    { sub_id: '407', title: 'Airport transfer (Klang Valley)' },
    { sub_id: '406', title: 'Kuala Lumpur Heritage Walking Tour' }, // 新服务
    { sub_id: '405', title: 'Sunway Lagoon Theme Park Shuttle' }      // 新服务
];

// 2. 服务检测和添加
chrome_inject_script({
    type: "MAIN",
    jsScript: `
        // 加载服务管理器
        class QRTranslationServiceManager {
            // ... (完整的类定义)
        }
        
        // 创建实例并处理
        const serviceManager = new QRTranslationServiceManager();
        const result = serviceManager.processSubProjects(${JSON.stringify(subProjects)});
        
        // 返回结果
        console.log('服务检测结果:', result);
        return JSON.stringify(result);
    `
});
```

### 预期输出

```json
{
    "totalProcessed": 4,
    "addedServices": 2,
    "existingServices": 2,
    "addedServiceDetails": [
        {
            "serviceTitle": "Kuala Lumpur Heritage Walking Tour",
            "serviceType": "tour",
            "directoryNumber": 10,
            "action": "added"
        },
        {
            "serviceTitle": "Sunway Lagoon Theme Park Shuttle",
            "serviceType": "general",
            "directoryNumber": 11,
            "action": "added"
        }
    ],
    "needsManualUpdate": true
}
```

## 服务类型识别规则

| 服务类型 | 关键词 | 示例 |
|---------|--------|------|
| airport | transfer, airport, klia, pickup, dropoff | "KLIA => Kuala Lumpur" |
| tour | tour, charter, private | "Kuala Lumpur Heritage Walking Tour" |
| ticket | ticket, feeding, firefly, eagle | "Eagle Feeding Ticket" |
| genting | genting, highlands | "Genting Highland Private Charter" |
| general | (其他) | "Sunway Lagoon Theme Park Shuttle" |

## 自动更新文件结构

### 更新 business-translation-tool.md

**目录部分**：
```markdown
## 目录
1.  [Kuala Lumpur City Tour (5 Hours)](#1-kuala-lumpur-city-tour-5-hours)
...
9.  [Point to Point Transfer Service (Genting)](#9-point-to-point-transfer-service-genting)
10. [Kuala Lumpur Heritage Walking Tour](#10-kuala-lumpur-heritage-walking-tour)  ← 新增
11. [Sunway Lagoon Theme Park Shuttle](#11-sunway-lagoon-theme-park-shuttle)      ← 新增
```

**待补充服务列表**：
```markdown
### 待补充服务列表

以下服务类型已识别但尚未补充完整翻译数据：

- **Kuala Lumpur Heritage Walking Tour** (tour类型) - 编号10
- **Sunway Lagoon Theme Park Shuttle** (general类型) - 编号11

**[此部分将在发现新服务时自动更新]**
```

## 用户操作指南

### 1. 发现新服务时的处理流程

1. **系统自动检测**：在翻译状态识别阶段自动执行
2. **查看检测结果**：注意控制台输出的新服务信息
3. **确认文件更新**：检查 `business-translation-tool.md` 是否已更新
4. **补充翻译内容**：手动为新服务添加具体的翻译内容

### 2. 手动补充翻译内容

对于新增的服务，需要：

1. **研究服务性质**：了解具体的服务内容和特点
2. **参考相似服务**：查看同类型服务的翻译格式
3. **编写标准描述**：为每种语言编写适当的描述和备注
4. **保持格式一致**：遵循现有的翻译格式标准

### 3. 质量检查清单

- [ ] 新服务的服务类型识别正确
- [ ] 目录编号连续且无重复
- [ ] 10种标准语言的表格结构完整
- [ ] 占位符 "[待补充]" 已替换为实际内容
- [ ] 翻译内容符合服务性质
- [ ] 格式与现有服务保持一致

## 错误处理

### 常见问题

1. **服务误判为新服务**
   - 检查关键词匹配规则
   - 调整相似度阈值
   - 手动验证服务是否真的是新类型

2. **文件更新失败**
   - 检查文件权限
   - 确认文件路径正确
   - 手动备份原文件

3. **目录编号冲突**
   - 重新计算最大编号
   - 检查现有目录结构
   - 手动调整编号

### 恢复操作

如果自动添加出现问题：

1. **恢复原文件**：从备份恢复 `business-translation-tool.md`
2. **手动添加**：按照标准格式手动添加新服务
3. **更新代码**：修正服务管理器的逻辑错误

## 最佳实践

1. **定期检查**：定期检查待补充服务列表
2. **及时补充**：发现新服务后尽快补充翻译内容
3. **保持更新**：确保翻译数据库与实际服务保持同步
4. **质量优先**：宁可暂停自动化，也要确保翻译质量

---

**版本**: 1.0  
**更新时间**: 2025-01-27  
**适用范围**: 所有QR码翻译管理项目 