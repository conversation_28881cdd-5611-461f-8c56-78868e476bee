# 标准化作业流程 (SOP): QR码全量审计与翻译状态验证

**版本**: 2.0
**核心原则**: 严格遵守项目制定的"最高优先级黄金操作守则 (ULTIMATE GOLDEN RULES v11.0)"，并完全采纳用户最新优化的10步流程，确保数据获取的准确性与操作的严谨性。

---

## 工作流程总览

本流程将对 `qrlist.md` 中的每一个QR项目严格执行，分为三个主要阶段：

1.  **环境准备与主列表（L0）操作**
2.  **子项目列表（L1）操作与数据提取**
3.  **翻译状态审查与处理（L2 -> L3）**

---

### **阶段一：环境准备与主列表（L0）操作**

1.  **【刷新页面】**:
    *   **操作**: 导航至目标网址 `https://staging.gomyhire.com.my/s/qrCode` 并执行一次完整的页面刷新。
    *   **目的**: 确保后续操作在一个纯净、无残留状态的页面环境中进行。

2.  **【主列表最大化】**:
    *   **操作**: 查找主页面QR码列表上方的 `custom-select` 分页控件。
    *   **执行**: 将其值设置为最大（通常是 "100"），以确保一次性显示所有QR项目，避免因分页而遗漏。

3.  **【定位并打开L1浮窗】**:
    *   **定位**: 根据 `qrlist.md` 的审计顺序，在页面中找到当前要审查的QR项目卡片。
    *   **操作**: 使用 `chrome_click_element` 工具模拟用户点击该卡片内的 **"Edit"** 按钮。
    *   **验证**: 操作后，必须立刻注入 `verifyModalVisible('qrCodeMainModal')` 脚本，确认L1浮窗 (`#qrCodeMainModal`) 已真实可见，而非仅仅存在于DOM中。

### **阶段二：子项目列表（L1）操作与数据提取**

4.  **【子列表最大化】**:
    *   **操作**: 在已打开并验证的L1浮窗内，找到子项目列表（Sub QR）上方的 `custom-select` 分页控件。
    *   **执行**: 将其值设置为最大（"100"）。
    *   **目的**: 确保L1浮窗内完整展示了该主项目下的**所有**子项目。

5.  **【提取准确数据】**:
    *   **操作**: 在L1浮窗的子项目列表已完全展示后，提取所有子项目的 **ID (Sub ID)** 和 **准确的项目名称 (Sub Project Name)**。
    *   **数据源**: 这是当前项目最新的、唯一可信的子项目清单。

6.  **【更新报告文件 - 第一部分】**:
    *   **操作**: 使用上一步获取的、最新的子项目列表，覆盖更新至对应的 `QR Code + ID/[id]_[name].md` 报告文件中。
    *   **目的**: 完成对SubQR ID和名称的重新审视和修正。

### **阶段三：翻译状态审查与处理（L2 -> L3）**

7.  **【循环打开L2翻译浮窗】**:
    *   **迭代**: 对L1浮窗中的**每一个子项目**，执行以下操作：
    *   **操作**: 点击其对应的 **"Translate"** 按钮。
    *   **验证**: 确认L2翻译浮窗 (`#qrCodeSubTranslateModal`) 已通过 `verifyModalVisible` 脚本验证为真实可见。

8.  **【审查翻译状态】**:
    *   **定位**: 在L2浮窗内，查找表格信息元素 `#qrCodeSubTranslateTable_info`。
    *   **判断**:
        *   如果内容为 "No data available in table"，则判定翻译条数为 **0**。
        *   如果内容为 "Showing 1 to X of Y entries"，则判定翻译条数为 **Y**。
    *   **目的**: 精确判断当前子项目的多语言翻译数量。

9.  **【决策与执行】**:
    *   **Case A: 条数为 10 (完整)**
        *   **A-1. 内容核对**: 进一步审视已有的10条翻译内容，是否与 `business-translation-tool.md` 中定义的标准模板完全一致。
        *   **A-2. 标记状态**:
            *   **若符合标准**: 在`.md`报告中将该子项目标记为 `✅ 10/10 翻译完整且内容标准`。
            *   **若不符合标准**: 标记为 `⚠️ 10/10 内容与标准不符`，并**记录下不一致的具体问题**，以便后续修正。
    *   **Case B: 条数 < 10 (不完整)**
        *   **B-1. 标记状态**: 在`.md`报告中标记当前状态，例如 `❌ 翻译不完整 (1/10)`。
        *   **B-2. 启动翻译补充流程**:
            *   点击"Add Translate"按钮打开L3浮窗。
            *   从 `business-translation-tool.md` 中查询该服务类型的标准翻译。
            *   将缺失语言的翻译内容填写到L3表单并提交。
            *   **B-3. 循环验证**: 添加一条翻译后，**流程将自动回到步骤8**，重新审视L2浮窗的翻译条数，并重复此过程，直到该子项目的翻译数量达到10条。

10. **【收尾与重置】**:
    *   **更新主列表**: 当一个主QR码项目下的所有子项目都完成审查和（可能的）补充后，更新 `qrlist.md` 中该项目的状态。
    *   **关闭浮窗**: 依次关闭L2和L1浮窗。
    *   **目的**: 重置操作环境，为下一个主QR码的审计做准备。 