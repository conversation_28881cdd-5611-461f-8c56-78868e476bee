/**
 * @file auto-fill-tnc.js
 * @description 自动填充GoMyHire QR码翻译系统中所有语言的TNC内容，并自动化点击相关按钮。
 * <AUTHOR> Assistant (calibrated by user)
 * @date 2024-07-30
 */

// ===============================================================================
// 翻译数据源 - GoMyHire QR码TNC内容多语言版本
// ===============================================================================
// 支持语言代码: zh-CN, zh-TW, en, ms, id, ja, ko, th, vi, ru
// 内容结构: 条款与条件 + 预订规则 + 客服投诉
// 最后更新: 2025-07-07
// ===============================================================================

const translations = {
    // 简体中文 (zh-CN) - 源语言版本
    'zh-CN': `一、条款与条件（Terms & Conditions Snapshot）

1. 服务主体与法律关系
服务提供方：GoMyHire Sdn. Bhd.（注册号 201901234567，下称「GoMyHire」）及其经书面授权的子公司、分销代理与地接运营商。
合同成立：扫描二维码→填写行程→点击「立即支付」即构成要约；付款成功且系统显示「预订已确认」时，服务合同成立并生效。
文件层级：若本 Snapshot 与完整版 T&C 存在冲突，以完整版为准；针对 QR 快速订单的特别优惠或附加费，以下单页即时提示为准。

2. 用户确认与信息义务
阅读义务：用户须在付款前完整阅读本文件；如有疑问，请使用「联系客服」或拨打服务热线后再行支付。
信息真实性：所填航班号、乘客信息、行李数量及联系方式必须真实准确；否则产生的等待费、改派费或服务中断由用户承担。

3. 服务范围与限制
可预订线路：机场⇆市区单程 / 往返接送（KLIA / KLIA2、Penang PEN、Kota Kinabalu BKI、Changi SIN 等）; 包车（日租、半日租、点对点接驳）。
暂不支持：需要额外签证或特殊通行证的跨境路线（如泰南、汶莱）、政府管制或高风险区域。

4. 车辆(随机分配)
• 4人座掀背车: Myvi, Iriz
• 5人座轿车: Saga, Bezza, Vios
• 加长型: Ertiga, Toyota Collora Cross
• 7人座SUV: Alza, Avanza
• 7人座MPV: New Alza, Aruz
• 7人座标准型: Toyota Fortuner, Exora, Innova
• 豪华MPV: Serena, Voxy
• 10人座: Staria, Starex
• 尊贵型: Toyota Alphard, Vellfire
• 18人座客货车: Placer X

5. 价格、支付与确认
费用组成：总价 = 基础租车费 + 预估路桥费
支付渠道：Visa、MasterCard、银联（3‑D Secure）、FPX、本地 eWallet（TnG）、支付宝跨境支付。
确认时效：付款后 ≤ 60 秒系统返回订单号并进入司机分配；> 3 分钟未确认将自动原路退款，避免重复支付。

6. 责任限制与免责
不可抗力：洪水、台风、疫情封锁、电信骨干中断、政府征用等不可控事件。
免责范围：因不可抗力取消或延误，GoMyHire 仅退还未履行部分费用，不承担转机延误或酒店损失等间接费用。
第三方责任：航空延误、道路事故、边检拥堵等第三方因素造成的时间损失，平台协助协调但不作额外赔付。

二、预订规则（Booking Rules Snapshot）

| 关键节点 | 费用及说明 |
|---------|-----------|
| 最迟下单 | 接送机 ≥ 6 h；包车/定制 ≥ 24 h（高峰期建议提前 48 h） |
| 司机分配 | 付款后 ≤ 30 min 内完成车辆锁定并推送司机姓名、车牌、车型与颜色 |
| 免费等待 | 接机 90 min（自航班落地计）；送机 30 min（司机到达计） |
| 超时计费 | 超免费时长后，每 30 min 按车型 RM 20–60 收费 |
| 免费取消 | 出发 ≥ 24 h，可自动全额退款 |
| 部分退款 | 6–24 h 取消：扣 20% 违约金 + 已发生空驶费 |
| 不可退款 | < 6 h 取消、乘客 No‑show 或现场更换目的地 > 20 km |
| 修改订单 | ≥ 24 h App 自助；< 24 h 客服人工，车型升级需补差价并收 10% 手续费 |

行李标准：
• 4座掀背车 (Myvi, Iriz)：2-3 人 + ≤ 2×24″ 行李箱
• 5座轿车 (Saga, Bezza, Vios)：3-4 人 + ≤ 2×28″ 或 3-4 人 + ≤ 3×24″
• 加长型 (Ertiga, Toyota Collora Cross)：4 人 + ≤ 3×24″ + 1×28″ 或 4-5 人 + ≤ 2×20″
• 7座SUV (Alza, Avanza)：5 人 + ≤ 3×20″ 或 4 人 + ≤ 4×24″
• 7座MPV (New Alza, Aruz)：5 人 + ≤ 3×20″ 或 4 人 + ≤ 4×24″
• 7座标准型 (Toyota Fortuner, Exora, Innova)：5 人 + ≤ 4×20″ 或 4 人 + ≤ 5×24″
• 豪华MPV (Serena, Voxy)：6 人 + ≤ 4×20″ 或 4 人 + ≤ 5×24″
• 10座 (Staria, Starex)：8-10 人 + ≤ 4×20″ 或 7 人 + ≤ 7×24″
• 尊贵型 (Toyota Alphard, Vellfire)：5-6 人 + ≤ 4×20″ 或 4 人 + ≤ 6×24″
• 18座客货车 (Placer X)：16 人 或 12 人 + ≤ 12×24″

附加服务：
• 儿童座椅: 额外 RM 30/张/次，现场临时需求视库存处理。
• 数据服务: 物理 SIM卡，RM10/日，无限高速流量，持热点共享。

温馨提示：
• 航班延误保障：延误 ≤ 2 h 司机自动延长等待；延误 2–4 h 免费改派一次；延误 > 4 h 可改约或全额退款。
• 保持联络：请在行李提取前开启移动数据或机场 Wi‑Fi，以便接收司机 WhatsApp/SMS。
• 联系方式：请在行李提取前打开手机数据或机场免费 Wi‑Fi，确保可接收 WhatsApp / 电邮。

三、客服与投诉
实时客服：App「帮助中心」24 × 7 在线聊天（平均响应 < 60 秒）。
电话支持：+6016‑223 4711（09:00–23:00 MYT）；00:00–09:00 请选择语音菜单 9 转值班经理。
电子邮件：<EMAIL>（承诺 24 h 内回复）。
投诉时效：行程结束 7 日内可提交书面投诉；平台 48 h 内首次反馈，5 个工作日内给出结论。

版权声明：本文件为 GoMyHire 版权所有，未经许可不得复制、截屏或用于非授权商业用途。版本号：QR‑TnC‑20250624‑CN‑1.2 © 2025 GoMyHire Sdn. Bhd. All Rights Reserved.`,

    // 繁体中文 (zh-TW) - 港澳台地区版本
    'zh-TW': `一、條款與條件（Terms & Conditions Snapshot）

1. 服務主體與法律關係
服務提供方：GoMyHire Sdn. Bhd.（註冊號 201901234567，下稱「GoMyHire」）及其經書面授權的子公司、分銷代理與地接運營商。
合同成立：掃描二維碼→填寫行程→點擊「立即支付」即構成要約；付款成功且系統顯示「預訂已確認」時，服務合同成立並生效。
文件層級：若本 Snapshot 與完整版 T&C 存在衝突，以完整版為準；針對 QR 快速訂單的特別優惠或附加費，以下單頁即時提示為準。

2. 用戶確認與信息義務
閱讀義務：用戶須在付款前完整閱讀本文件；如有疑問，請使用「聯繫客服」或撥打服務熱線後再行支付。
信息真實性：所填航班號、乘客信息、行李數量及聯繫方式必須真實準確；否則產生的等待費、改派費或服務中斷由用戶承擔。

3. 服務範圍與限制
可預訂線路：機場⇆市區單程 / 往返接送（KLIA / KLIA2、Penang PEN、Kota Kinabalu BKI、Changi SIN 等）; 包車（日租、半日租、點對點接駁）。
暫不支持：需要額外簽證或特殊通行證的跨境路線（如泰南、汶萊）、政府管制或高風險區域。

4. 車輛(隨機分配)
• 4人座掀背車: Myvi, Iriz
• 5人座轎車: Saga, Bezza, Vios
• 加長型: Ertiga, Toyota Collora Cross
• 7人座SUV: Alza, Avanza
• 7人座MPV: New Alza, Aruz
• 7人座標準型: Toyota Fortuner, Exora, Innova
• 豪華MPV: Serena, Voxy
• 10人座: Staria, Starex
• 尊貴型: Toyota Alphard, Vellfire
• 18人座客貨車: Placer X

5. 價格、支付與確認
費用組成：總價 = 基礎租車費 + 預估路橋費
支付渠道：Visa、MasterCard、銀聯（3‑D Secure）、FPX、本地 eWallet（TnG）、支付寶跨境支付。
確認時效：付款後 ≤ 60 秒系統返回訂單號並進入司機分配；> 3 分鐘未確認將自動原路退款，避免重複支付。

6. 責任限制與免責
不可抗力：洪水、颱風、疫情封鎖、電信骨幹中斷、政府徵用等不可控事件。
免責範圍：因不可抗力取消或延誤，GoMyHire 僅退還未履行部分費用，不承擔轉機延誤或酒店損失等間接費用。
第三方責任：航空延誤、道路事故、邊檢擁堵等第三方因素造成的時間損失，平台協助協調但不作額外賠付。

二、預訂規則（Booking Rules Snapshot）

| 關鍵節點 | 費用及說明 |
|---------|-----------|
| 最遲下單 | 接送機 ≥ 6 h；包車/定製 ≥ 24 h（高峰期建議提前 48 h） |
| 司機分配 | 付款後 ≤ 30 min 內完成車輛鎖定並推送司機姓名、車牌、車型與顏色 |
| 免費等待 | 接機 90 min（自航班落地計）；送機 30 min（司機到達計） |
| 超時計費 | 超免費時長後，每 30 min 按車型 RM 20–60 收費 |
| 免費取消 | 出發 ≥ 24 h，可自動全額退款 |
| 部分退款 | 6–24 h 取消：扣 20% 違約金 + 已發生空駛費 |
| 不可退款 | < 6 h 取消、乘客 No‑show 或現場更換目的地 > 20 km |
| 修改訂單 | ≥ 24 h App 自助；< 24 h 客服人工，車型升級需補差價並收 10% 手續費 |

行李標準：
• 4座掀背車 (Myvi, Iriz)：2-3 人 + ≤ 2×24″ 行李箱
• 5座轎車 (Saga, Bezza, Vios)：3-4 人 + ≤ 2×28″ 或 3-4 人 + ≤ 3×24″
• 加長型 (Ertiga, Toyota Collora Cross)：4 人 + ≤ 3×24″ + 1×28″ 或 4-5 人 + ≤ 2×20″
• 7座SUV (Alza, Avanza)：5 人 + ≤ 3×20″ 或 4 人 + ≤ 4×24″
• 7座MPV (New Alza, Aruz)：5 人 + ≤ 3×20″ 或 4 人 + ≤ 4×24″
• 7座標準型 (Toyota Fortuner, Exora, Innova)：5 人 + ≤ 4×20″ 或 4 人 + ≤ 5×24″
• 豪華MPV (Serena, Voxy)：6 人 + ≤ 4×20″ 或 4 人 + ≤ 5×24″
• 10座 (Staria, Starex)：8-10 人 + ≤ 4×20″ 或 7 人 + ≤ 7×24″
• 尊貴型 (Toyota Alphard, Vellfire)：5-6 人 + ≤ 4×20″ 或 4 人 + ≤ 6×24″
• 18座客貨車 (Placer X)：16 人 或 12 人 + ≤ 12×24″

附加服務：
• 兒童座椅: 額外 RM 30/張/次，現場臨時需求視庫存處理。
• 數據服務: 物理 SIM卡，RM10/日，無限高速流量，持熱點共享。

溫馨提示：
• 航班延誤保障：延誤 ≤ 2 h 司機自動延長等待；延誤 2–4 h 免費改派一次；延誤 > 4 h 可改約或全額退款。
• 保持聯絡：請在行李提取前開啟移動數據或機場 Wi‑Fi，以便接收司機 WhatsApp/SMS。
• 聯繫方式：請在行李提取前打開手機數據或機場免費 Wi‑Fi，確保可接收 WhatsApp / 電郵。

三、客服與投訴
實時客服：App「幫助中心」24 × 7 在線聊天（平均響應 < 60 秒）。
電話支持：+6016‑223 4711（09:00–23:00 MYT）；00:00–09:00 請選擇語音菜單 9 轉值班經理。
電子郵件：<EMAIL>（承諾 24 h 內回覆）。
投訴時效：行程結束 7 日內可提交書面投訴；平台 48 h 內首次反饋，5 個工作日內給出結論。

版權聲明：本文件為 GoMyHire 版權所有，未經許可不得複製、截屏或用於非授權商業用途。版本號：QR‑TnC‑20250624‑TW‑1.2 © 2025 GoMyHire Sdn. Bhd. All Rights Reserved.`,
    
    // 英语 (en) - 国际通用版本
    'en': `I. Terms & Conditions (Snapshot)

1. Service Provider and Legal Relationship
Service Provider: GoMyHire Sdn. Bhd. (Registration No. 201901234567, hereinafter "GoMyHire") and its duly authorized subsidiaries, distribution agents, and local operators.
Contract Formation: Scanning the QR code → Filling in the itinerary → Clicking "Pay Now" constitutes an offer. The service contract is formed and effective upon successful payment and system display of "Booking Confirmed".
Document Hierarchy: In case of conflict between this Snapshot and the full T&C, the full version shall prevail. For special offers or surcharges on QR Express Orders, the real-time prompts on the booking page will apply.

2. User Acknowledgment and Information Obligation
Duty to Read: Users must read this document in its entirety before payment. For inquiries, please use "Contact Customer Service" or call the service hotline before proceeding with payment.
Information Accuracy: The provided flight number, passenger information, luggage quantity, and contact details must be true and accurate. Any resulting waiting fees, re-dispatch fees, or service interruptions shall be borne by the user.

3. Scope of Service and Limitations
Available Routes: Airport ⇆ City one-way / round-trip transfers (KLIA / KLIA2, Penang PEN, Kota Kinabalu BKI, Changi SIN, etc.); Private car charter (full-day, half-day, point-to-point transfer).
Not Supported: Cross-border routes requiring extra visas or special permits (e.g., Southern Thailand, Brunei), government-regulated or high-risk areas.

4. Vehicle (Randomly Assigned)
• 4-seater hatchback: Myvi, Iriz
• 5-seater sedan: Saga, Bezza, Vios
• Extended: Ertiga, Toyota Collora Cross
• 7-seater SUV: Alza, Avanza
• 7-seater MPV: New Alza, Aruz
• 7-seater standard: Toyota Fortuner, Exora, Innova
• Luxury MPV: Serena, Voxy
• 10-seater: Staria, Starex
• Premium: Toyota Alphard, Vellfire
• 18-seater van: Placer X

5. Pricing, Payment, and Confirmation
Total Price Composition: Base Car Rental Fee + Estimated Tolls
Payment Channels: Visa, MasterCard, UnionPay (3-D Secure), FPX, local eWallets (TnG), Alipay Cross-border Payment.
Confirmation Time: An order number will be returned within ≤ 60 seconds after payment, and driver allocation will commence. If not confirmed within > 3 minutes, the payment will be automatically refunded to avoid double charges.

6. Limitation of Liability and Disclaimer
Force Majeure: Uncontrollable events such as floods, typhoons, pandemic lockdowns, telecommunication backbone disruptions, government requisitions, etc.
Disclaimer Scope: For cancellations or delays due to force majeure, GoMyHire will only refund the unfulfilled portion of the service fee and will not be liable for indirect costs such as connecting flight delays or hotel losses.
Third-Party Liability: Time losses due to third-party factors such as flight delays, road accidents, or border congestion will be coordinated by the platform but without additional compensation.

II. Booking Rules (Snapshot)

| Key Point | Fees & Description |
|-----------|-------------------|
| Latest Booking | Airport transfer ≥ 6 h; Car charter/customized ≥ 24 h (48 h advance booking recommended during peak season) |
| Driver Assignment | Vehicle locked and driver's name, license plate, vehicle model, and color sent within ≤ 30 min after payment |
| Free Waiting Time | Airport pickup: 90 min (from flight landing); Airport drop-off: 30 min (from driver arrival) |
| Overtime Charges | After the free waiting period, RM 20–60 per 30 min depending on the vehicle type |
| Free Cancellation | Full refund for cancellations made ≥ 24 h before departure |
| Partial Refund | For cancellations made 6–24 h before: 20% penalty fee + any empty run costs incurred |
| Non-refundable | For cancellations < 6 h, passenger no-show, or on-site destination change > 20 km |
| Order Modification | Self-service on App ≥ 24 h before; Customer service for < 24 h, with a 10% handling fee and price difference for vehicle upgrades |

Luggage Allowance:
• 4-seater hatchback (Myvi, Iriz): 2-3 people + ≤ 2×24″ suitcases
• 5-seater sedan (Saga, Bezza, Vios): 3-4 people + ≤ 2×28″ or 3-4 people + ≤ 3×24″
• Extended (Ertiga, Toyota Collora Cross): 4 people + ≤ 3×24″ + 1×28″ or 4-5 people + ≤ 2×20″
• 7-seater SUV (Alza, Avanza): 5 people + ≤ 3×20″ or 4 people + ≤ 4×24″
• 7-seater MPV (New Alza, Aruz): 5 people + ≤ 3×20″ or 4 people + ≤ 4×24″
• 7-seater standard (Toyota Fortuner, Exora, Innova): 5 people + ≤ 4×20″ or 4 people + ≤ 5×24″
• Luxury MPV (Serena, Voxy): 6 people + ≤ 4×20″ or 4 people + ≤ 5×24″
• 10-seater (Staria, Starex): 8-10 people + ≤ 4×20″ or 7 people + ≤ 7×24″
• Premium (Toyota Alphard, Vellfire): 5-6 people + ≤ 4×20″ or 4 people + ≤ 6×24″
• 18-seater van (Placer X): 16 people or 12 people + ≤ 12×24″

Additional Services:
• Child Seat: Extra RM 30/seat/trip, subject to availability for on-site requests.
• Data Service: Physical SIM card, RM10/day, unlimited high-speed data with hotspot sharing.

Helpful Tips:
• Flight Delay Guarantee: For delays ≤ 2 h, the driver will wait automatically. For delays of 2–4 h, one free rescheduling is offered. For delays > 4 h, you can reschedule or get a full refund.
• Stay Connected: Please turn on mobile data or connect to airport Wi-Fi before baggage claim to receive WhatsApp/SMS from the driver.
• Contact Info: Please ensure you can receive WhatsApp / Email by turning on mobile data or using airport free Wi-Fi before collecting your luggage.

III. Customer Service and Complaints
Real-time Support: 24/7 online chat in the App's "Help Center" (average response time < 60 seconds).
Phone Support: +6016‑223 4711 (09:00–23:00 MYT); from 00:00–09:00, select option 9 for the duty manager.
Email: <EMAIL> (response within 24 hours guaranteed).
Complaint Period: Written complaints can be submitted within 7 days of trip completion. The platform will provide an initial response within 48 hours and a conclusion within 5 working days.

Copyright Notice: This document is the copyright of GoMyHire. It may not be copied, screenshotted, or used for unauthorized commercial purposes. Version: QR‑TnC‑20250624‑EN‑1.2 © 2025 GoMyHire Sdn. Bhd. All Rights Reserved.`,
    
    // 马来语 (ms) - 马来西亚官方语言版本
    'ms': `I. Terma & Syarat (Ringkasan)

1. Penyedia Perkhidmatan dan Hubungan Undang-undang
Penyedia Perkhidmatan: GoMyHire Sdn. Bhd. (No. Pendaftaran 201901234567, selepas ini "GoMyHire") dan anak-anak syarikat, ejen pengedar, serta pengendali tempatan yang diberi kuasa secara bertulis.
Pembentukan Kontrak: Mengimbas kod QR → Mengisi butiran perjalanan → Menekan "Bayar Sekarang" merupakan satu tawaran. Kontrak perkhidmatan terbentuk dan berkuat kuasa setelah pembayaran berjaya dan sistem memaparkan "Tempahan Disahkan".
Hierarki Dokumen: Jika terdapat percanggahan antara Ringkasan ini dan T&S penuh, versi penuh akan diguna pakai. Untuk tawaran istimewa atau surcaj bagi Tempahan Ekspres QR, makluman masa nyata di halaman tempahan akan terpakai.

2. Pengakuan Pengguna dan Kewajipan Maklumat
Kewajipan Membaca: Pengguna mesti membaca dokumen ini sepenuhnya sebelum membuat pembayaran. Jika ada pertanyaan, sila gunakan "Hubungi Khidmat Pelanggan" atau hubungi talian hotline perkhidmatan sebelum meneruskan pembayaran.
Ketepatan Maklumat: Nombor penerbangan, maklumat penumpang, jumlah bagasi, dan butiran hubungan yang diberikan mestilah benar dan tepat. Sebarang bayaran menunggu, bayaran penghantaran semula, atau gangguan perkhidmatan yang timbul akan ditanggung oleh pengguna.

3. Skop Perkhidmatan dan Had
Laluan Tersedia: Pindahan sehala / pergi balik Lapangan Terbang ⇆ Bandar (KLIA / KLIA2, Penang PEN, Kota Kinabalu BKI, Changi SIN dll.); sewa kereta persendirian (sehari penuh, separuh hari, pindahan dari titik ke titik).
Tidak Disokong: Laluan rentas sempadan yang memerlukan visa tambahan atau permit khas (cth., Selatan Thailand, Brunei), kawasan yang dikawal oleh kerajaan atau berisiko tinggi.

4. Kenderaan (Diagihkan Secara Rawak)
• 4 tempat duduk hatchback: Myvi, Iriz
• 5 tempat duduk sedan: Saga, Bezza, Vios
• Dipanjangkan: Ertiga, Toyota Collora Cross
• 7 tempat duduk SUV: Alza, Avanza
• 7 tempat duduk MPV: New Alza, Aruz
• 7 tempat duduk standard: Toyota Fortuner, Exora, Innova
• MPV mewah: Serena, Voxy
• 10 tempat duduk: Staria, Starex
• Premium: Toyota Alphard, Vellfire
• Van 18 tempat duduk: Placer X

5. Harga, Pembayaran, dan Pengesahan
Komposisi Harga: Jumlah = Yuran Asas Sewa Kereta + Anggaran Tol
Saluran Pembayaran: Visa, MasterCard, UnionPay (3-D Secure), FPX, e-dompet tempatan (TnG), Pembayaran Rentas Sempadan Alipay.
Masa Pengesahan: Nombor tempahan akan diberikan dalam masa ≤ 60 saat selepas pembayaran, dan peruntukan pemandu akan dimulakan. Jika tidak disahkan dalam masa > 3 minit, pembayaran akan dibatalkan secara automatik untuk mengelakkan caj berganda.

6. Had Liabiliti dan Penafian
Force Majeure: Peristiwa yang tidak dapat dikawal seperti banjir, taufan, sekatan pergerakan pandemik, gangguan tulang belakang telekomunikasi, pengambilan alih oleh kerajaan, dll.
Skop Penafian: Untuk pembatalan atau kelewatan disebabkan oleh force majeure, GoMyHire hanya akan memulangkan bahagian yuran perkhidmatan yang belum dilaksanakan dan tidak akan bertanggungjawab ke atas kos tidak langsung seperti kelewatan penerbangan sambungan atau kerugian hotel.
Liabiliti Pihak Ketiga: Kehilangan masa disebabkan oleh faktor pihak ketiga seperti kelewatan penerbangan, kemalangan jalan raya, atau kesesakan di sempadan akan diselaraskan oleh platform tetapi tanpa pampasan tambahan.

II. Peraturan Tempahan (Ringkasan)

| Perkara Penting | Yuran & Penerangan |
|----------------|-------------------|
| Tempahan Terakhir | Pindahan lapangan terbang ≥ 6 j; Sewa kereta/khas ≥ 24 j (tempahan 48 j lebih awal disyorkan semasa musim puncak) |
| Peruntukan Pemandu | Kenderaan dikunci dan nama pemandu, nombor plat, model kenderaan, dan warna dihantar dalam masa ≤ 30 min selepas pembayaran |
| Masa Menunggu Percuma | Ambil di lapangan terbang: 90 min (dari pendaratan penerbangan); Hantar ke lapangan terbang: 30 min (dari ketibaan pemandu) |
| Caj Lebih Masa | Selepas tempoh menunggu percuma, RM 20–60 setiap 30 min bergantung pada jenis kenderaan |
| Pembatalan Percuma | Bayaran balik penuh untuk pembatalan yang dibuat ≥ 24 j sebelum perlepasan |
| Bayaran Balik Sebahagian | Untuk pembatalan yang dibuat 6–24 j sebelum: yuran penalti 20% + sebarang kos perjalanan kosong yang telah ditanggung |
| Tidak Boleh Dikembalikan | Untuk pembatalan < 6 j, penumpang tidak hadir, atau penukaran destinasi di lokasi > 20 km |
| Pengubahsuaian Tempahan | Layan diri di App ≥ 24 j sebelum; Khidmat pelanggan untuk < 24 j, dengan yuran pengendalian 10% dan perbezaan harga untuk menaik taraf kenderaan |

Elaun Bagasi:
• 4 tempat duduk hatchback (Myvi, Iriz): 2-3 orang + ≤ 2×24″ beg pakaian
• 5 tempat duduk sedan (Saga, Bezza, Vios): 3-4 orang + ≤ 2×28″ atau 3-4 orang + ≤ 3×24″
• Dipanjangkan (Ertiga, Toyota Collora Cross): 4 orang + ≤ 3×24″ + 1×28″ atau 4-5 orang + ≤ 2×20″
• 7 tempat duduk SUV (Alza, Avanza): 5 orang + ≤ 3×20″ atau 4 orang + ≤ 4×24″
• 7 tempat duduk MPV (New Alza, Aruz): 5 orang + ≤ 3×20″ atau 4 orang + ≤ 4×24″
• 7 tempat duduk standard (Toyota Fortuner, Exora, Innova): 5 orang + ≤ 4×20″ atau 4 orang + ≤ 5×24″
• MPV mewah (Serena, Voxy): 6 orang + ≤ 4×20″ atau 4 orang + ≤ 5×24″
• 10 tempat duduk (Staria, Starex): 8-10 orang + ≤ 4×20″ atau 7 orang + ≤ 7×24″
• Premium (Toyota Alphard, Vellfire): 5-6 orang + ≤ 4×20″ atau 4 orang + ≤ 6×24″
• Van 18 tempat duduk (Placer X): 16 orang atau 12 orang + ≤ 12×24″

Perkhidmatan Tambahan:
• Kerusi Kanak-kanak: Tambahan RM 30/kerusi/perjalanan, tertakluk pada ketersediaan untuk permintaan di lokasi.
• Perkhidmatan Data: Kad SIM fizikal, RM10/hari, data berkelajuan tinggi tanpa had dengan perkongsian hotspot.

Petua Berguna:
• Jaminan Kelewatan Penerbangan: Untuk kelewatan ≤ 2 j, pemandu akan menunggu secara automatik. Untuk kelewatan 2–4 j, penjadualan semula percuma sekali ditawarkan. Untuk kelewatan > 4 j, anda boleh menjadualkan semula atau mendapat bayaran balik penuh.
• Kekal Berhubung: Sila hidupkan data mudah alih atau sambung ke Wi-Fi lapangan terbang sebelum menuntut bagasi untuk menerima WhatsApp/SMS daripada pemandu.
• Maklumat Hubungan: Sila pastikan anda boleh menerima WhatsApp / E-mel dengan menghidupkan data mudah alih atau menggunakan Wi-Fi percuma lapangan terbang sebelum mengambil bagasi anda.

III. Khidmat Pelanggan dan Aduan
Sokongan Masa Nyata: Sembang dalam talian 24/7 di "Pusat Bantuan" App (purata masa tindak balas < 60 saat).
Sokongan Telefon: +6016‑223 4711 (09:00–23:00 MYT); dari 00:00–09:00, pilih opsyen 9 untuk pengurus bertugas.
E-mel: <EMAIL> (tindak balas dalam masa 24 jam dijamin).
Tempoh Aduan: Aduan bertulis boleh dihantar dalam masa 7 hari selepas perjalanan selesai. Platform akan memberikan maklum balas awal dalam masa 48 jam dan kesimpulan dalam masa 5 hari bekerja.

Notis Hak Cipta: Dokumen ini adalah hak cipta GoMyHire. Ia tidak boleh disalin, diambil tangkapan skrin, atau digunakan untuk tujuan komersial yang tidak dibenarkan. Versi: QR‑TnC‑20250624‑MS‑1.2 © 2025 GoMyHire Sdn. Bhd. All Rights Reserved.`,

    // Bahasa Indonesia
    'id': `I. Syarat & Ketentuan (Ringkasan)

1. Penyedia Layanan dan Hubungan Hukum
Penyedia Layanan: GoMyHire Sdn. Bhd. (No. Registrasi 201901234567, selanjutnya disebut "GoMyHire") beserta anak perusahaan, agen distribusi, dan operator lokal yang berwenang secara tertulis.
Pembentukan Kontrak: Memindai kode QR → Mengisi rincian perjalanan → Mengeklik "Bayar Sekarang" merupakan sebuah penawaran. Kontrak layanan terbentuk dan berlaku setelah pembayaran berhasil dan sistem menampilkan "Pemesanan Dikonfirmasi".
Hierarki Dokumen: Jika terjadi pertentangan antara Ringkasan ini dan S&K lengkap, versi lengkap yang akan berlaku. Untuk penawaran khusus atau biaya tambahan pada Pesanan Cepat QR, pemberitahuan waktu nyata di halaman pemesanan akan berlaku.

2. Pengakuan Pengguna dan Kewajiban Informasi
Kewajiban Membaca: Pengguna harus membaca dokumen ini secara keseluruhan sebelum melakukan pembayaran. Jika ada pertanyaan, silakan gunakan "Hubungi Layanan Pelanggan" atau hubungi hotline layanan sebelum melanjutkan pembayaran.
Keakuratan Informasi: Nomor penerbangan, informasi penumpang, jumlah bagasi, dan rincian kontak yang diberikan harus benar dan akurat. Biaya tunggu, biaya pengiriman ulang, atau gangguan layanan yang timbul akan ditanggung oleh pengguna.

3. Lingkup Layanan dan Batasan
Rute yang Tersedia: Transfer sekali jalan / pulang-pergi Bandara ⇆ Kota (KLIA / KLIA2, Penang PEN, Kota Kinabalu BKI, Changi SIN, dll.); sewa mobil pribadi (sehari penuh, setengah hari, transfer dari titik ke titik).
Tidak Didukung: Rute lintas batas yang memerlukan visa tambahan atau izin khusus (misalnya, Thailand Selatan, Brunei), area yang diatur pemerintah atau berisiko tinggi.

4. Kendaraan (Ditugaskan Secara Acak)
4-seater hatchback: Myvi, Iriz
5-Seater Sedan: Saga, Bezza, Vios
Extended: Ertiga, Toyota Collora Cross
7-Seater SUV: Alza, Avanza
7-Seater MPV: New Alza, Aruz
7-Seater Standard: Toyota Fortuner, Exora, Innova
Luxury MPV: Serena, Voxy
10-seater: Staria, Starex
Premium: Toyota Alphard, Vellfire
18-Seater Van: Placer X

5. Harga, Pembayaran, dan Konfirmasi
Komposisi Harga Total = Biaya Dasar Sewa Mobil + Estimasi Tol
Saluran Pembayaran: Visa, MasterCard, UnionPay (3-D Secure), FPX, e-wallet lokal (TnG), Pembayaran Lintas Batas Alipay.
Waktu Konfirmasi: Nomor pesanan akan diberikan dalam ≤ 60 detik setelah pembayaran, dan alokasi pengemudi akan dimulai. Jika tidak dikonfirmasi dalam > 3 menit, pembayaran akan dikembalikan secara otomatis untuk menghindari biaya ganda.

6. Batasan Tanggung Jawab dan Penafian
Force Majeure: Peristiwa yang tidak dapat dikendalikan seperti banjir, topan, karantina wilayah pandemi, gangguan jaringan telekomunikasi, permintaan pemerintah, dll.
Lingkup Penafian: Untuk pembatalan atau penundaan karena force majeure, GoMyHire hanya akan mengembalikan bagian biaya layanan yang belum terpenuhi dan tidak akan bertanggung jawab atas biaya tidak langsung seperti penundaan penerbangan lanjutan atau kerugian hotel.
Tanggung Jawab Pihak Ketiga: Kehilangan waktu karena faktor pihak ketiga seperti penundaan penerbangan, kecelakaan di jalan, atau kemacetan di perbatasan akan dikoordinasikan oleh platform tetapi tanpa kompensasi tambahan.

II. Aturan Pemesanan (Ringkasan)
Poin Penting | Biaya & Deskripsi
--- | ---
Pemesanan Terakhir | Transfer bandara ≥ 6 jam; Sewa mobil/kustom ≥ 24 jam (pemesanan 48 jam sebelumnya disarankan selama musim ramai)
Penugasan Pengemudi | Kendaraan dikunci dan nama pengemudi, nomor polisi, model kendaraan, dan warna dikirim dalam ≤ 30 menit setelah pembayaran
Waktu Tunggu Gratis | Penjemputan di bandara: 90 menit (dari pendaratan penerbangan); Pengantaran ke bandara: 30 menit (dari kedatangan pengemudi)
Biaya Lembur | Setelah periode tunggu gratis, RM 20–60 per 30 menit tergantung pada jenis kendaraan
Pembatalan Gratis | Pengembalian dana penuh untuk pembatalan yang dilakukan ≥ 24 jam sebelum keberangkatan
Pengembalian Dana Sebagian | Untuk pembatalan yang dilakukan 6–24 jam sebelumnya: denda 20% + biaya perjalanan kosong yang telah terjadi
Tidak Dapat Dikembalikan | Untuk pembatalan < 6 jam, penumpang tidak muncul, atau perubahan tujuan di lokasi > 20 km
Modifikasi Pesanan | Layanan mandiri di Aplikasi ≥ 24 jam sebelumnya; Layanan pelanggan untuk < 24 jam, dengan biaya penanganan 10% dan selisih harga untuk peningkatan kendaraan

Jatah Bagasi
4-seater hatchback (Myvi, Iriz): 2-3 orang + ≤ 2×24″ koper
5-seater sedan (Saga, Bezza, Vios): 3-4 orang + ≤ 2×28″ atau 3-4 orang + ≤ 3×24″
Extended (Ertiga, Toyota Collora Cross): 4 orang + ≤ 3×24″ + 1×28″ atau 4-5 orang + ≤ 2×20″
7-seater SUV (Alza, Avanza): 5 orang + ≤ 3×20″ atau 4 orang + ≤ 4×24″
7-seater MPV (New Alza, Aruz): 5 orang + ≤ 3×20″ atau 4 orang + ≤ 4×24″
7-seater standard (Toyota Fortuner, Exora, Innova): 5 orang + ≤ 4×20″ atau 4 orang + ≤ 5×24″
Luxury MPV (Serena, Voxy): 6 orang + ≤ 4×20″ atau 4 orang + ≤ 5×24″
10-seater (Staria, Starex): 8-10 orang + ≤ 4×20″ atau 7 orang + ≤ 7×24″
Premium (Toyota Alphard, Vellfire): 5-6 orang + ≤ 4×20″ atau 4 orang + ≤ 6×24″
18-seater van (Placer X): 16 orang atau 12 orang + ≤ 12×24″

Kursi Anak: Tambahan RM 30/kursi/perjalanan, tergantung ketersediaan untuk permintaan di lokasi.
Layanan Data: Kartu SIM fisik, RM10/hari, data berkecepatan tinggi tanpa batas dengan berbagi hotspot.

Tips Bermanfaat
Jaminan Keterlambatan Penerbangan: Untuk keterlambatan ≤ 2 jam, pengemudi akan menunggu secara otomatis. Untuk keterlambatan 2–4 jam, penjadwalan ulang gratis sekali ditawarkan. Untuk keterlambatan > 4 jam, Anda dapat menjadwal ulang atau mendapatkan pengembalian dana penuh.
Tetap Terhubung: Harap aktifkan data seluler atau sambungkan ke Wi-Fi bandara sebelum mengambil bagasi untuk menerima WhatsApp/SMS dari pengemudi.
Info Kontak: Harap pastikan Anda dapat menerima WhatsApp / Email dengan mengaktifkan data seluler atau menggunakan Wi-Fi gratis bandara sebelum mengambil bagasi Anda.

III. Layanan Pelanggan dan Keluhan
Dukungan Waktu Nyata: Obrolan online 24/7 di "Pusat Bantuan" Aplikasi (waktu respons rata-rata < 60 detik).
Dukungan Telepon: +6016‑223 4711 (09:00–23:00 MYT); dari 00:00–09:00, pilih opsi 9 untuk manajer yang bertugas.
Email: <EMAIL> (respons dalam 24 jam dijamin).
Periode Keluhan: Keluhan tertulis dapat diajukan dalam 7 hari setelah perjalanan selesai. Platform akan memberikan respons awal dalam 48 jam dan kesimpulan dalam 5 hari kerja.

Pemberitahuan Hak Cipta: Dokumen ini adalah hak cipta GoMyHire. Tidak boleh disalin, diambil tangkapan layar, atau digunakan untuk tujuan komersial yang tidak sah. Versi: QR‑TnC‑20250624‑ID‑1.2 © 2025 GoMyHire Sdn. Bhd. Hak Cipta Dilindungi.`,
    
    // 日本語
    'ja': `I. 利用規約（スナップショット）

1. サービス提供者と法的関係
サービス提供者: GoMyHire Sdn. Bhd.（登録番号 201901234567、以下「GoMyHire」）およびその正規に承認された子会社、販売代理店、現地オペレーター。
契約の成立: QRコードをスキャン→旅程を記入→「今すぐ支払う」をクリックするとオファーとなります。支払いが成功し、システムに「予約確定」と表示された時点で、サービス契約が成立し有効となります。
文書の優先順位: このスナップショットと完全版の利用規約に矛盾がある場合は、完全版が優先されます。QRエクスプレスオーダーの特別オファーや追加料金については、予約ページのリアルタイムの表示が適用されます。

2. ユーザーの確認と情報提供義務
読了義務: ユーザーは支払い前に本書を完全に読む必要があります。不明な点がある場合は、「カスタマーサービスに連絡」を使用するか、サービスホットラインに電話してから支払いを続行してください。
情報の正確性: 提供されたフライト番号、乗客情報、荷物の数量、連絡先は真実かつ正確でなければなりません。その結果生じる待機料金、再配車料金、またはサービスの中断は、ユーザーの負担となります。

3. サービス範囲と制限
利用可能なルート: 空港⇆市内間の片道/往復送迎（KLIA / KLIA2、ペナンPEN、コタキナバルBKI、チャンギSINなど）; プライベートチャーター（終日、半日、地点間送迎）。
サポート対象外: 追加のビザや特別許可が必要な国境を越えるルート（例：タイ南部、ブルネイ）、政府規制地域または高リスク地域。

4. 車両（ランダム割り当て）
4人乗りハッチバック: Myvi, Iriz
5人乗りセダン: Saga, Bezza, Vios
延長型: Ertiga, Toyota Collora Cross
7人乗りSUV: Alza, Avanza
7人乗りMPV: New Alza, Aruz
7人乗り標準: Toyota Fortuner, Exora, Innova
ラグジュアリーMPV: Serena, Voxy
10人乗り: Staria, Starex
プレミアム: Toyota Alphard, Vellfire
18人乗りバン: Placer X

5. 価格、支払い、確認
合計価格構成 = 基本レンタル料 + 推定有料道路料金
支払い方法: Visa、MasterCard、UnionPay（3Dセキュア）、FPX、ローカルeWallet（TnG）、Alipay越境支払い。
確認時間: 支払い後≤60秒以内に注文番号が返され、ドライバーの割り当てが開始されます。>3分以内に確認されない場合、二重請求を避けるために支払いは自動的に返金されます。

6. 責任の制限と免責事項
不可抗力: 洪水、台風、パンデミックによるロックダウン、電気通信基幹回線の障害、政府による徴発などの制御不能な事象。
免責範囲: 不可抗力によるキャンセルまたは遅延の場合、GoMyHireは未履行部分のサービス料金のみを返金し、乗り継ぎ便の遅延やホテルの損失などの間接的な費用については責任を負いません。
第三者の責任: フライトの遅延、交通事故、国境の混雑など、第三者の要因による時間の損失については、プラットフォームが調整を支援しますが、追加の補償は行いません。

II. 予約規則（スナップショット）
キーポイント | 料金と説明
--- | ---
最終予約 | 空港送迎≥6時間; チャーター/カスタム≥24時間（ピークシーズンは48時間前の予約を推奨）
ドライバー割り当て | 支払い後≤30分以内に車両を確保し、ドライバーの名前、ナンバープレート、車種、色を送信
無料待機時間 | 空港出迎え：90分（フライト到着時から）; 空港見送り：30分（ドライバー到着時から）
超過料金 | 無料待機時間を超えた後、車種に応じて30分ごとにRM 20–60
無料キャンセル | 出発≥24時間前のキャンセルは全額返金
一部返金 | 出発6〜24時間前のキャンセル：20％の違約金+発生した空車走行費用
返金不可 | 出発<6時間前のキャンセル、乗客のノーショー、または現地での目的地変更>20 km
注文の変更 | 出発≥24時間前はアプリでセルフサービス; <24時間前はカスタマーサービス対応、車両アップグレードには差額と10％の手数料が必要

手荷物許容量
4人乗りハッチバック (Myvi, Iriz): 2-3人+≤2×24インチスーツケース
5人乗りセダン (Saga, Bezza, Vios): 3-4人+≤2×28インチまたは3-4人+≤3×24インチ
延長型 (Ertiga, Toyota Collora Cross): 4人+≤3×24インチ+1×28インチまたは4-5人+≤2×20インチ
7人乗りSUV (Alza, Avanza): 5人+≤3×20インチまたは4人+≤4×24インチ
7人乗りMPV (New Alza, Aruz): 5人+≤3×20インチまたは4人+≤4×24インチ
7人乗り標準 (Toyota Fortuner, Exora, Innova): 5人+≤4×20インチまたは4人+≤5×24インチ
ラグジュアリーMPV (Serena, Voxy): 6人+≤4×20インチまたは4人+≤5×24インチ
10人乗り (Staria, Starex): 8-10人+≤4×20インチまたは7人+≤7×24インチ
プレミアム (Toyota Alphard, Vellfire): 5-6人+≤4×20インチまたは4人+≤6×24インチ
18人乗りバン (Placer X): 16人または12人+≤12×24インチ

チャイルドシート: 追加RM 30/席/回、現地でのリクエストは在庫状況によります。
データサービス: 物理SIMカード、RM10/日、ホットスポット共有付きの高速データ無制限。

役立つヒント
フライト遅延保証: 遅延≤2時間の場合、ドライバーは自動的に待機します。遅延2〜4時間の場合、1回の無料再スケジュールが提供されます。遅延>4時間の場合、再スケジュールまたは全額返金が可能です。
接続を維持: 荷物を受け取る前にモバイルデータまたは空港Wi-Fiをオンにして、ドライバーからのWhatsApp/SMSを受信できるようにしてください。
連絡先情報: 荷物を受け取る前にモバイルデータまたは空港の無料Wi-Fiをオンにして、WhatsApp / Eメールを受信できるようにしてください。

III. カスタマーサービスと苦情
リアルタイムサポート: アプリの「ヘルプセンター」での24時間年中無休のオンラインチャット（平均応答時間<60秒）。
電話サポート: +6016‑223 4711（09:00–23:00 MYT）; 00:00–09:00は、オプション9で当直マネージャーを選択してください。
メール: <EMAIL>（24時間以内の返信を保証）。
苦情申し立て期間: 旅行終了後7日以内に書面で苦情を申し立てることができます。プラットフォームは48時間以内に初期応答を提供し、5営業日以内に結論を出します。

著作権表示: この文書の著作権はGoMyHireに帰属します。無断でコピー、スクリーンショット、または無許可の商業目的で使用することはできません。バージョン: QR‑TnC‑20250624‑JA‑1.2 © 2025 GoMyHire Sdn. Bhd. All Rights Reserved.`,

    // 한국어
    'ko': `I. 이용 약관 (요약)

1. 서비스 제공자 및 법적 관계
서비스 제공자: GoMyHire Sdn. Bhd. (등록 번호 201901234567, 이하 "GoMyHire") 및 정식으로 승인된 자회사, 유통 대리점 및 현지 운영 업체.
계약 체결: QR 코드 스캔 → 여정 입력 → "지금 결제" 클릭은 청약을 구성합니다. 결제가 성공하고 시스템에 "예약 확정"이 표시되면 서비스 계약이 체결되고 효력이 발생합니다.
문서 계층: 본 요약본과 전체 이용 약관이 상충하는 경우 전체 버전이 우선합니다. QR 익스프레스 주문의 특별 할인 또는 추가 요금에 대해서는 예약 페이지의 실시간 안내가 적용됩니다.

2. 사용자 확인 및 정보 제공 의무
읽을 의무: 사용자는 결제 전에 이 문서를 완전히 읽어야 합니다. 문의 사항이 있는 경우 결제를 진행하기 전에 "고객 서비스에 문의"를 이용하거나 서비스 핫라인에 전화하십시오.
정보 정확성: 제공된 항공편 번호, 승객 정보, 수하물 수량 및 연락처는 사실이고 정확해야 합니다. 그로 인해 발생하는 대기 요금, 재배차 요금 또는 서비스 중단은 사용자가 부담합니다.

3. 서비스 범위 및 제한
이용 가능한 노선: 공항 ⇆ 시내 편도 / 왕복 이동 (KLIA / KLIA2, 페낭 PEN, 코타키나발루 BKI, 창이 SIN 등); 개인 차량 대절 (종일, 반일, 지점 간 이동).
지원되지 않음: 추가 비자나 특별 허가가 필요한 국경 간 노선 (예: 태국 남부, 브루나이), 정부 규제 또는 고위험 지역.

4. 차량 (무작위 배정)
4인승 해치백: Myvi, Iriz
5인승 세단: Saga, Bezza, Vios
확장형: Ertiga, Toyota Collora Cross
7인승 SUV: Alza, Avanza
7인승 MPV: New Alza, Aruz
7인승 표준: Toyota Fortuner, Exora, Innova
럭셔리 MPV: Serena, Voxy
10인승: Staria, Starex
프리미엄: Toyota Alphard, Vellfire
18인승 밴: Placer X

5. 가격, 결제 및 확인
총 가격 구성 = 기본 렌터카 요금 + 예상 통행료
결제 채널: Visa, MasterCard, UnionPay (3-D Secure), FPX, 현지 eWallet (TnG), Alipay 국경 간 결제.
확인 시간: 결제 후 ≤ 60초 이내에 주문 번호가 반환되고 운전자 배정이 시작됩니다. > 3분 이내에 확인되지 않으면 이중 청구를 피하기 위해 결제가 자동으로 환불됩니다.

6. 책임의 제한 및 면책
불가항력: 홍수, 태풍, 전염병 봉쇄, 통신 기간망 중단, 정부 징발 등 통제 불가능한 사건.
면책 범위: 불가항력으로 인한 취소 또는 지연의 경우, GoMyHire는 이행되지 않은 서비스 요금 부분만 환불하며, 연결 항공편 지연이나 호텔 손실과 같은 간접 비용에 대해서는 책임지지 않습니다.
제3자 책임: 항공편 지연, 도로 사고 또는 국경 혼잡과 같은 제3자 요인으로 인한 시간 손실은 플랫폼이 조정을 지원하지만 추가 보상은 하지 않습니다.

II. 예약 규칙 (요약)
핵심 사항 | 요금 및 설명
--- | ---
최종 예약 | 공항 이동 ≥ 6시간; 차량 대절/맞춤 ≥ 24시간 (성수기에는 48시간 전 예약 권장)
운전자 배정 | 결제 후 ≤ 30분 이내에 차량을 확보하고 운전자 이름, 번호판, 차종, 색상을 전송
무료 대기 시간 | 공항 픽업: 90분 (항공편 착륙 시점부터); 공항 드롭오프: 30분 (운전자 도착 시점부터)
초과 요금 | 무료 대기 시간 이후 차종에 따라 30분당 RM 20–60
무료 취소 | 출발 ≥ 24시간 전 취소 시 전액 환불
부분 환불 | 출발 6~24시간 전 취소 시: 20% 위약금 + 발생한 공차 운행 비용
환불 불가 | 출발 < 6시간 전 취소, 승객 노쇼 또는 현장에서 목적지 변경 > 20km
주문 수정 | 출발 ≥ 24시간 전 앱에서 셀프 서비스; < 24시간 전 고객 서비스, 차량 업그레이드 시 차액 및 10% 수수료 필요

수하물 허용량
4인승 해치백 (Myvi, Iriz): 2-3명 + ≤ 2×24인치 여행 가방
5인승 세단 (Saga, Bezza, Vios): 3-4명 + ≤ 2×28인치 또는 3-4명 + ≤ 3×24인치
확장형 (Ertiga, Toyota Collora Cross): 4명 + ≤ 3×24인치 + 1×28인치 또는 4-5명 + ≤ 2×20인치
7인승 SUV (Alza, Avanza): 5명 + ≤ 3×20인치 또는 4명 + ≤ 4×24인치
7인승 MPV (New Alza, Aruz): 5명 + ≤ 3×20인치 또는 4명 + ≤ 4×24인치
7인승 표준 (Toyota Fortuner, Exora, Innova): 5명 + ≤ 4×20인치 또는 4명 + ≤ 5×24인치
럭셔리 MPV (Serena, Voxy): 6명 + ≤ 4×20인치 또는 4명 + ≤ 5×24인치
10인승 (Staria, Starex): 8-10명 + ≤ 4×20인치 또는 7명 + ≤ 7×24인치
프리미엄 (Toyota Alphard, Vellfire): 5-6명 + ≤ 4×20인치 또는 4명 + ≤ 6×24인치
18인승 밴 (Placer X): 16명 또는 12명 + ≤ 12×24인치

어린이 좌석: 추가 RM 30/좌석/회, 현장 요청 시 재고 상황에 따라 다름.
데이터 서비스: 물리적 SIM 카드, RM10/일, 핫스팟 공유 기능이 있는 무제한 고속 데이터.

유용한 팁
항공편 지연 보장: 지연 ≤ 2시간의 경우 운전자가 자동으로 대기합니다. 지연 2~4시간의 경우 1회 무료 재예약이 제공됩니다. 지연 > 4시간의 경우 재예약 또는 전액 환불이 가능합니다.
연결 유지: 수하물을 찾기 전에 운전자로부터 WhatsApp/SMS를 수신할 수 있도록 모바일 데이터 또는 공항 Wi-Fi를 켜주십시오.
연락처 정보: 수하물을 수령하기 전에 모바일 데이터 또는 공항 무료 Wi-Fi를 켜서 WhatsApp / 이메일을 수신할 수 있는지 확인하십시오.

III. 고객 서비스 및 불만 사항
실시간 지원: 앱의 "도움말 센터"에서 연중무휴 24시간 온라인 채팅 (평균 응답 시간 < 60초).
전화 지원: +6016‑223 4711 (09:00–23:00 MYT); 00:00–09:00에는 옵션 9를 선택하여 당직 관리자에게 연결하십시오.
이메일: <EMAIL> (24시간 이내 응답 보장).
불만 제기 기간: 여행 종료 후 7일 이내에 서면으로 불만을 제기할 수 있습니다. 플랫폼은 48시간 이내에 초기 응답을 제공하고 5영업일 이내에 결론을 내립니다.

저작권 고지: 이 문서의 저작권은 GoMyHire에 있습니다. 허가 없이 복사, 스크린샷 또는 무단 상업적 목적으로 사용할 수 없습니다. 버전: QR‑TnC‑20250624‑KO‑1.2 © 2025 GoMyHire Sdn. Bhd. All Rights Reserved.`,

    // ภาษาไทย
    'th': `I. ข้อกำหนดและเงื่อนไข (ภาพรวม)

1. ผู้ให้บริการและความสัมพันธ์ทางกฎหมาย
ผู้ให้บริการ: GoMyHire Sdn. Bhd. (หมายเลขทะเบียน 201901234567 ซึ่งต่อไปนี้จะเรียกว่า "GoMyHire") และบริษัทในเครือ ตัวแทนจำหน่าย และผู้ประกอบการในท้องถิ่นที่ได้รับอนุญาตอย่างเป็นทางการ
การทำสัญญา: การสแกนรหัส QR → การกรอกรายละเอียดการเดินทาง → การคลิก "ชำระเงินทันที" ถือเป็นการเสนอสัญญา สัญญาบริการจะเกิดขึ้นและมีผลบังคับเมื่อการชำระเงินสำเร็จและระบบแสดงข้อความ "ยืนยันการจองแล้ว"
ลำดับชั้นของเอกสาร: ในกรณีที่มีข้อขัดแย้งระหว่างภาพรวมนี้กับข้อกำหนดและเงื่อนไขฉบับเต็ม ให้ยึดถือฉบับเต็มเป็นหลัก สำหรับข้อเสนอพิเศษหรือค่าบริการเพิ่มเติมสำหรับคำสั่งซื้อด่วนผ่าน QR ให้ยึดตามการแจ้งเตือนแบบเรียลไทม์ในหน้าการจอง

2. การรับทราบของผู้ใช้และภาระหน้าที่ในการให้ข้อมูล
หน้าที่ในการอ่าน: ผู้ใช้ต้องอ่านเอกสารนี้ทั้งหมดก่อนชำระเงิน หากมีข้อสงสัย โปรดใช้ "ติดต่อฝ่ายบริการลูกค้า" หรือโทรสายด่วนบริการก่อนดำเนินการชำระเงิน
ความถูกต้องของข้อมูล: หมายเลขเที่ยวบิน ข้อมูลผู้โดยสาร จำนวนสัมภาระ และรายละเอียดการติดต่อที่ให้ไว้ต้องเป็นจริงและถูกต้อง ค่าธรรมเนียมการรอ ค่าธรรมเนียมการจัดส่งใหม่ หรือการหยุดชะงักของบริการที่เกิดขึ้นจะเป็นภาระของผู้ใช้

3. ขอบเขตของบริการและข้อจำกัด
เส้นทางที่ให้บริการ: บริการรับส่งเที่ยวเดียว / ไปกลับ สนามบิน ⇆ เมือง (KLIA / KLIA2, ปีนัง PEN, โกตากินะบะลู BKI, ชางงี SIN ฯลฯ); บริการรถเช่าส่วนตัว (เต็มวัน, ครึ่งวัน, รับส่งแบบจุดต่อจุด)
ไม่รองรับ: เส้นทางข้ามพรมแดนที่ต้องใช้วีซ่าเพิ่มเติมหรือใบอนุญาตพิเศษ (เช่น ภาคใต้ของไทย, บรูไน), พื้นที่ที่ควบคุมโดยรัฐบาลหรือมีความเสี่ยงสูง

4. ยานพาหนะ (จัดสรรแบบสุ่ม)
รถแฮทช์แบ็ก 4 ที่นั่ง: Myvi, Iriz
รถเก๋ง 5 ที่นั่ง: Saga, Bezza, Vios
รถขยาย: Ertiga, Toyota Collora Cross
รถ SUV 7 ที่นั่ง: Alza, Avanza
รถ MPV 7 ที่นั่ง: New Alza, Aruz
รถมาตรฐาน 7 ที่นั่ง: Toyota Fortuner, Exora, Innova
รถ MPV หรู: Serena, Voxy
รถ 10 ที่นั่ง: Staria, Starex
รถพรีเมียม: Toyota Alphard, Vellfire
รถตู้ 18 ที่นั่ง: Placer X

5. ราคา การชำระเงิน และการยืนยัน
องค์ประกอบราคาทั้งหมด = ค่าเช่ารถพื้นฐาน + ค่าผ่านทางโดยประมาณ
ช่องทางการชำระเงิน: Visa, MasterCard, UnionPay (3-D Secure), FPX, eWallet ท้องถิ่น (TnG), การชำระเงินข้ามพรมแดน Alipay
เวลายืนยัน: หมายเลขคำสั่งซื้อจะถูกส่งคืนภายใน ≤ 60 วินาทีหลังจากการชำระเงิน และจะเริ่มการจัดสรรคนขับ หากไม่ได้รับการยืนยันภายใน > 3 นาที การชำระเงินจะถูกคืนเงินโดยอัตโนมัติเพื่อหลีกเลี่ยงการเรียกเก็บเงินซ้ำ

6. ข้อจำกัดความรับผิดและข้อสงวนสิทธิ์
เหตุสุดวิสัย: เหตุการณ์ที่ควบคุมไม่ได้ เช่น น้ำท่วม, พายุไต้ฝุ่น, การปิดเมืองเนื่องจากการระบาดใหญ่, การหยุดชะงักของเครือข่ายโทรคมนาคม, การเรียกใช้โดยรัฐบาล เป็นต้น
ขอบเขตข้อสงวนสิทธิ์: สำหรับการยกเลิกหรือความล่าช้าเนื่องจากเหตุสุดวิสัย GoMyHire จะคืนเงินเฉพาะส่วนของค่าบริการที่ยังไม่ได้ดำเนินการและจะไม่รับผิดชอบต่อค่าใช้จ่ายทางอ้อม เช่น ความล่าช้าของเที่ยวบินต่อเครื่องหรือความเสียหายของโรงแรม
ความรับผิดของบุคคลที่สาม: การสูญเสียเวลาเนื่องจากปัจจัยของบุคคลที่สาม เช่น ความล่าช้าของเที่ยวบิน, อุบัติเหตุบนท้องถนน, หรือความแออัดที่ชายแดนจะได้รับการประสานงานโดยแพลตฟอร์ม แต่ไม่มีการชดเชยเพิ่มเติม

II. กฎการจอง (ภาพรวม)
ประเด็นสำคัญ | ค่าธรรมเนียมและคำอธิบาย
--- | ---
การจองล่าสุด | บริการรับส่งสนามบิน ≥ 6 ชม.; รถเช่า/กำหนดเอง ≥ 24 ชม. (แนะนำให้จองล่วงหน้า 48 ชม. ในช่วงฤดูท่องเที่ยว)
การจัดสรรคนขับ | ยานพาหนะจะถูกล็อคและชื่อคนขับ, ป้ายทะเบียน, รุ่นรถ, และสีจะถูกส่งภายใน ≤ 30 นาทีหลังจากการชำระเงิน
เวลารอฟรี | รับที่สนามบิน: 90 นาที (นับจากเวลาเครื่องลงจอด); ส่งที่สนามบิน: 30 นาที (นับจากเวลาที่คนขับมาถึง)
ค่าบริการล่วงเวลา | หลังจากหมดเวลารอฟรี จะคิดค่าบริการ RM 20–60 ทุก 30 นาที ขึ้นอยู่กับประเภทยานพาหนะ
การยกเลิกฟรี | คืนเงินเต็มจำนวนสำหรับการยกเลิกที่ทำ ≥ 24 ชม. ก่อนออกเดินทาง
การคืนเงินบางส่วน | สำหรับการยกเลิกที่ทำ 6–24 ชม. ก่อน: ค่าปรับ 20% + ค่าใช้จ่ายในการวิ่งรถเปล่าที่เกิดขึ้น
ไม่สามารถคืนเงินได้ | สำหรับการยกเลิก < 6 ชม., ผู้โดยสารไม่มาปรากฏตัว, หรือการเปลี่ยนจุดหมายปลายทาง ณ สถานที่ > 20 กม.
การแก้ไขคำสั่งซื้อ | บริการตนเองบนแอป ≥ 24 ชม. ก่อน; ฝ่ายบริการลูกค้าสำหรับ < 24 ชม. พร้อมค่าธรรมเนียมการจัดการ 10% และส่วนต่างราคาสำหรับการอัปเกรดยานพาหนะ

น้ำหนักสัมภาระที่อนุญาต
รถแฮทช์แบ็ก 4 ที่นั่ง (Myvi, Iriz): 2-3 คน + ≤ 2×24″ กระเป๋าเดินทาง
รถเก๋ง 5 ที่นั่ง (Saga, Bezza, Vios): 3-4 คน + ≤ 2×28″ หรือ 3-4 คน + ≤ 3×24″
รถขยาย (Ertiga, Toyota Collora Cross): 4 คน + ≤ 3×24″ + 1×28″ หรือ 4-5 คน + ≤ 2×20″
รถ SUV 7 ที่นั่ง (Alza, Avanza): 5 คน + ≤ 3×20″ หรือ 4 คน + ≤ 4×24″
รถ MPV 7 ที่นั่ง (New Alza, Aruz): 5 คน + ≤ 3×20″ หรือ 4 คน + ≤ 4×24″
รถมาตรฐาน 7 ที่นั่ง (Toyota Fortuner, Exora, Innova): 5 คน + ≤ 4×20″ หรือ 4 คน + ≤ 5×24″
รถ MPV หรู (Serena, Voxy): 6 คน + ≤ 4×20″ หรือ 4 คน + ≤ 5×24″
รถ 10 ที่นั่ง (Staria, Starex): 8-10 คน + ≤ 4×20″ หรือ 7 คน + ≤ 7×24″
รถพรีเมียม (Toyota Alphard, Vellfire): 5-6 คน + ≤ 4×20″ หรือ 4 คน + ≤ 6×24″
รถตู้ 18 ที่นั่ง (Placer X): 16 คน หรือ 12 คน + ≤ 12×24″

ที่นั่งเด็ก: เพิ่ม RM 30/ที่นั่ง/เที่ยว, ขึ้นอยู่กับความพร้อมให้บริการสำหรับคำขอ ณ สถานที่
บริการข้อมูล: ซิมการ์ด, RM10/วัน, ข้อมูลความเร็วสูงไม่จำกัดพร้อมการแชร์ฮอตสปอต

เคล็ดลับที่เป็นประโยชน์
การรับประกันความล่าช้าของเที่ยวบิน: สำหรับความล่าช้า ≤ 2 ชม. คนขับจะรอโดยอัตโนมัติ สำหรับความล่าช้า 2–4 ชม. จะมีการเปลี่ยนกำหนดการฟรีหนึ่งครั้ง สำหรับความล่าช้า > 4 ชม. คุณสามารถเปลี่ยนกำหนดการหรือขอคืนเงินเต็มจำนวนได้
การเชื่อมต่อ: โปรดเปิดข้อมูลมือถือหรือเชื่อมต่อกับ Wi-Fi ของสนามบินก่อนรับกระเป๋าเพื่อรับ WhatsApp/SMS จากคนขับ
ข้อมูลติดต่อ: โปรดตรวจสอบให้แน่ใจว่าคุณสามารถรับ WhatsApp / อีเมลได้โดยการเปิดข้อมูลมือถือหรือใช้ Wi-Fi ฟรีของสนามบินก่อนรับกระเป๋า

III. ฝ่ายบริการลูกค้าและข้อร้องเรียน
การสนับสนุนแบบเรียลไทม์: แชทออนไลน์ 24/7 ใน "ศูนย์ช่วยเหลือ" ของแอป (เวลาตอบกลับโดยเฉลี่ย < 60 วินาที)
การสนับสนุนทางโทรศัพท์: +6016‑223 4711 (09:00–23:00 MYT); ตั้งแต่ 00:00–09:00 เลือกตัวเลือก 9 สำหรับผู้จัดการเวร
อีเมล: <EMAIL> (รับประกันการตอบกลับภายใน 24 ชั่วโมง)
ระยะเวลาการร้องเรียน: สามารถส่งข้อร้องเรียนเป็นลายลักษณ์อักษรได้ภายใน 7 วันหลังสิ้นสุดการเดินทาง แพลตฟอร์มจะให้การตอบกลับเบื้องต้นภายใน 48 ชั่วโมงและข้อสรุปภายใน 5 วันทำการ

ประกาศเกี่ยวกับลิขสิทธิ์: เอกสารนี้เป็นลิขสิทธิ์ของ GoMyHire ห้ามคัดลอก ถ่ายภาพหน้าจอ หรือใช้เพื่อวัตถุประสงค์ทางการค้าโดยไม่ได้รับอนุญาต เวอร์ชัน: QR‑TnC‑20250624‑TH‑1.2 © 2025 GoMyHire Sdn. Bhd. สงวนลิขสิทธิ์

`,

    // Tiếng Việt
    'vi': `I. Điều khoản & Điều kiện (Tóm tắt)

1. Nhà cung cấp dịch vụ và Mối quan hệ pháp lý
Nhà cung cấp dịch vụ: GoMyHire Sdn. Bhd. (Số đăng ký 201901234567, sau đây gọi là "GoMyHire") và các công ty con, đại lý phân phối và nhà điều hành địa phương được ủy quyền hợp lệ.
Hình thành hợp đồng: Quét mã QR → Điền thông tin hành trình → Nhấp vào "Thanh toán ngay" tạo thành một đề nghị. Hợp đồng dịch vụ được hình thành và có hiệu lực khi thanh toán thành công và hệ thống hiển thị "Đặt chỗ đã xác nhận".
Thứ bậc tài liệu: Trong trường hợp có xung đột giữa bản Tóm tắt này và Điều khoản & Điều kiện đầy đủ, phiên bản đầy đủ sẽ được ưu tiên áp dụng. Đối với các ưu đãi đặc biệt hoặc phụ phí cho Đơn hàng Nhanh qua QR, các thông báo thời gian thực trên trang đặt chỗ sẽ được áp dụng.

2. Ghi nhận của người dùng và Nghĩa vụ thông tin
Nghĩa vụ đọc: Người dùng phải đọc toàn bộ tài liệu này trước khi thanh toán. Nếu có thắc mắc, vui lòng sử dụng "Liên hệ Dịch vụ khách hàng" hoặc gọi đến đường dây nóng dịch vụ trước khi tiếp tục thanh toán.
Tính chính xác của thông tin: Số hiệu chuyến bay, thông tin hành khách, số lượng hành lý và chi tiết liên lạc được cung cấp phải trung thực và chính xác. Mọi khoản phí chờ đợi, phí điều phối lại hoặc gián đoạn dịch vụ phát sinh sẽ do người dùng chịu.

3. Phạm vi dịch vụ và Giới hạn
Các tuyến đường có sẵn: Dịch vụ đưa đón một chiều / khứ hồi Sân bay ⇆ Thành phố (KLIA / KLIA2, Penang PEN, Kota Kinabalu BKI, Changi SIN, v.v.); thuê xe riêng (nguyên ngày, nửa ngày, đưa đón điểm-điểm).
Không hỗ trợ: Các tuyến đường xuyên biên giới yêu cầu thị thực bổ sung hoặc giấy phép đặc biệt (ví dụ: Miền Nam Thái Lan, Brunei), các khu vực do chính phủ quy định hoặc có rủi ro cao.

4. Phương tiện (Phân bổ ngẫu nhiên)
Xe hatchback 4 chỗ: Myvi, Iriz
Xe sedan 5 chỗ: Saga, Bezza, Vios
Xe mở rộng: Ertiga, Toyota Collora Cross
Xe SUV 7 chỗ: Alza, Avanza
Xe MPV 7 chỗ: New Alza, Aruz
Xe tiêu chuẩn 7 chỗ: Toyota Fortuner, Exora, Innova
Xe MPV hạng sang: Serena, Voxy
Xe 10 chỗ: Staria, Starex
Xe cao cấp: Toyota Alphard, Vellfire
Xe van 18 chỗ: Placer X

5. Giá cả, Thanh toán và Xác nhận
Thành phần tổng giá = Phí thuê xe cơ bản + Phí cầu đường ước tính
Kênh thanh toán: Visa, MasterCard, UnionPay (3-D Secure), FPX, ví điện tử địa phương (TnG), Thanh toán xuyên biên giới Alipay.
Thời gian xác nhận: Số đơn hàng sẽ được trả về trong vòng ≤ 60 giây sau khi thanh toán và việc phân bổ tài xế sẽ bắt đầu. Nếu không được xác nhận trong vòng > 3 phút, khoản thanh toán sẽ được tự động hoàn trả để tránh tính phí hai lần.

6. Giới hạn trách nhiệm và Miễn trừ trách nhiệm
Bất khả kháng: Các sự kiện không thể kiểm soát như lũ lụt, bão, phong tỏa do đại dịch, gián đoạn đường trục viễn thông, trưng dụng của chính phủ, v.v.
Phạm vi miễn trừ trách nhiệm: Đối với việc hủy bỏ hoặc chậm trễ do bất khả kháng, GoMyHire sẽ chỉ hoàn lại phần phí dịch vụ chưa được thực hiện và sẽ không chịu trách nhiệm về các chi phí gián tiếp như chậm chuyến bay nối chuyến hoặc thiệt hại khách sạn.
Trách nhiệm của bên thứ ba: Mất thời gian do các yếu tố của bên thứ ba như chậm chuyến bay, tai nạn giao thông hoặc tắc nghẽn ở biên giới sẽ được nền tảng phối hợp nhưng không có bồi thường bổ sung.

II. Quy tắc đặt chỗ (Tóm tắt)
Điểm chính | Phí & Mô tả
--- | ---
Đặt chỗ muộn nhất | Đưa đón sân bay ≥ 6 giờ; Thuê xe/tùy chỉnh ≥ 24 giờ (khuyến nghị đặt trước 48 giờ trong mùa cao điểm)
Phân bổ tài xế | Xe được khóa và tên tài xế, biển số xe, kiểu xe và màu sắc được gửi trong vòng ≤ 30 phút sau khi thanh toán
Thời gian chờ miễn phí | Đón tại sân bay: 90 phút (tính từ khi máy bay hạ cánh); Đưa ra sân bay: 30 phút (tính từ khi tài xế đến)
Phí làm thêm giờ | Sau thời gian chờ miễn phí, RM 20–60 mỗi 30 phút tùy thuộc vào loại xe
Hủy miễn phí | Hoàn tiền đầy đủ cho các yêu cầu hủy được thực hiện ≥ 24 giờ trước khi khởi hành
Hoàn tiền một phần | Đối với các yêu cầu hủy được thực hiện từ 6–24 giờ trước: phí phạt 20% + bất kỳ chi phí chạy xe trống nào phát sinh
Không hoàn lại tiền | Đối với các yêu cầu hủy < 6 giờ, hành khách không có mặt hoặc thay đổi điểm đến tại chỗ > 20 km
Sửa đổi đơn hàng | Tự phục vụ trên Ứng dụng ≥ 24 giờ trước; Dịch vụ khách hàng cho < 24 giờ, với phí xử lý 10% và chênh lệch giá cho việc nâng cấp xe

Quy định về hành lý
Xe hatchback 4 chỗ (Myvi, Iriz): 2-3 người + ≤ 2×24″ vali
Xe sedan 5 chỗ (Saga, Bezza, Vios): 3-4 người + ≤ 2×28″ hoặc 3-4 người + ≤ 3×24″
Xe mở rộng (Ertiga, Toyota Collora Cross): 4 người + ≤ 3×24″ + 1×28″ hoặc 4-5 người + ≤ 2×20″
Xe SUV 7 chỗ (Alza, Avanza): 5 người + ≤ 3×20″ hoặc 4 người + ≤ 4×24″
Xe MPV 7 chỗ (New Alza, Aruz): 5 người + ≤ 3×20″ hoặc 4 người + ≤ 4×24″
Xe tiêu chuẩn 7 chỗ (Toyota Fortuner, Exora, Innova): 5 người + ≤ 4×20″ hoặc 4 người + ≤ 5×24″
Xe MPV hạng sang (Serena, Voxy): 6 người + ≤ 4×20″ hoặc 4 người + ≤ 5×24″
Xe 10 chỗ (Staria, Starex): 8-10 người + ≤ 4×20″ hoặc 7 người + ≤ 7×24″
Xe cao cấp (Toyota Alphard, Vellfire): 5-6 người + ≤ 4×20″ hoặc 4 người + ≤ 6×24″
Xe van 18 chỗ (Placer X): 16 người hoặc 12 người + ≤ 12×24″

Ghế trẻ em: Phụ phí 30 RM/ghế/chuyến, tùy thuộc vào tình trạng sẵn có cho các yêu cầu tại chỗ.
Dịch vụ dữ liệu: Thẻ SIM vật lý, 10 RM/ngày, dữ liệu tốc độ cao không giới hạn với chia sẻ điểm phát sóng.

Mẹo hữu ích
Đảm bảo chậm chuyến bay: Đối với các chuyến bay bị chậm ≤ 2 giờ, tài xế sẽ tự động đợi. Đối với các chuyến bay bị chậm 2–4 giờ, được cung cấp một lần sắp xếp lại miễn phí. Đối với các chuyến bay bị chậm > 4 giờ, bạn có thể sắp xếp lại hoặc được hoàn tiền đầy đủ.
Giữ liên lạc: Vui lòng bật dữ liệu di động hoặc kết nối với Wi-Fi sân bay trước khi nhận hành lý để nhận WhatsApp/SMS từ tài xế.
Thông tin liên hệ: Vui lòng đảm bảo bạn có thể nhận được WhatsApp / Email bằng cách bật dữ liệu di động hoặc sử dụng Wi-Fi miễn phí của sân bay trước khi lấy hành lý.

III. Dịch vụ khách hàng và Khiếu nại
Hỗ trợ thời gian thực: Trò chuyện trực tuyến 24/7 trong "Trung tâm trợ giúp" của Ứng dụng (thời gian phản hồi trung bình < 60 giây).
Hỗ trợ qua điện thoại: +6016‑223 4711 (09:00–23:00 MYT); từ 00:00–09:00, chọn tùy chọn 9 cho quản lý trực.
Email: <EMAIL> (đảm bảo phản hồi trong vòng 24 giờ).
Thời hạn khiếu nại: Khiếu nại bằng văn bản có thể được gửi trong vòng 7 ngày sau khi kết thúc chuyến đi. Nền tảng sẽ cung cấp phản hồi ban đầu trong vòng 48 giờ và kết luận trong vòng 5 ngày làm việc.

Thông báo bản quyền: Tài liệu này thuộc bản quyền của GoMyHire. Không được sao chép, chụp màn hình hoặc sử dụng cho các mục đích thương mại trái phép. Phiên bản: QR‑TnC‑20250624‑VI‑1.2 © 2025 GoMyHire Sdn. Bhd. Mọi quyền được bảo lưu.`,

    // Русский
    'ru': `I. Условия и положения (краткий обзор)

1. Поставщик услуг и правовые отношения
Поставщик услуг: GoMyHire Sdn. Bhd. (регистрационный номер 201901234567, далее «GoMyHire») и его должным образом уполномоченные дочерние компании, дистрибьюторы и местные операторы.
Заключение договора: Сканирование QR-кода → Заполнение маршрута → Нажатие «Оплатить сейчас» является офертой. Договор на обслуживание заключается и вступает в силу после успешной оплаты и отображения системой сообщения «Бронирование подтверждено».
Иерархия документов: В случае расхождений между этим кратким обзором и полной версией Условий и положений, полная версия имеет преимущественную силу. Для специальных предложений или доплат по экспресс-заказам QR применяются подсказки в режиме реального времени на странице бронирования.

2. Подтверждение пользователя и обязанность предоставления информации
Обязанность ознакомиться: Пользователи должны полностью прочитать этот документ перед оплатой. При возникновении вопросов, пожалуйста, воспользуйтесь «Связаться со службой поддержки» или позвоните на горячую линию перед оплатой.
Точность информации: Предоставленный номер рейса, информация о пассажирах, количество багажа и контактные данные должны быть достоверными и точными. Любые возникающие сборы за ожидание, повторную отправку или перерывы в обслуживании несет пользователь.

3. Объем услуг и ограничения
Доступные маршруты: Трансферы в одну сторону / туда и обратно Аэропорт ⇆ Город (KLIA / KLIA2, Пенанг PEN, Кота-Кинабалу BKI, Чанги SIN и др.); аренда частного автомобиля (на полный день, на полдня, трансфер от точки до точки).
Не поддерживается: Трансграничные маршруты, требующие дополнительных виз или специальных разрешений (например, Южный Таиланд, Бруней), зоны, регулируемые государством, или зоны повышенного риска.

4. Транспортное средство (назначается случайным образом)
4-местный хэтчбек: Myvi, Iriz
5-местный седан: Saga, Bezza, Vios
Удлиненный: Ertiga, Toyota Collora Cross
7-местный внедорожник: Alza, Avanza
7-местный минивэн: New Alza, Aruz
7-местный стандарт: Toyota Fortuner, Exora, Innova
Люкс-минивэн: Serena, Voxy
10-местный: Staria, Starex
Премиум: Toyota Alphard, Vellfire
18-местный фургон: Placer X

5. Цены, оплата и подтверждение
Состав общей стоимости = Базовая стоимость аренды автомобиля + предполагаемые дорожные сборы
Каналы оплаты: Visa, MasterCard, UnionPay (3-D Secure), FPX, местные электронные кошельки (TnG), трансграничные платежи Alipay.
Время подтверждения: Номер заказа будет возвращен в течение ≤ 60 секунд после оплаты, и начнется распределение водителей. Если подтверждение не получено в течение > 3 минут, платеж будет автоматически возвращен во избежание двойного списания.

6. Ограничение ответственности и отказ от ответственности
Форс-мажор: Неконтролируемые события, такие как наводнения, тайфуны, карантин из-за пандемии, сбои в магистральных сетях телекоммуникаций, реквизиция правительством и т.д.
Область отказа от ответственности: В случае отмены или задержки из-за форс-мажора GoMyHire возмещает только неоказанную часть услуги и не несет ответственности за косвенные убытки, такие как задержки стыковочных рейсов или убытки отелей.
Ответственность третьих лиц: Потеря времени из-за факторов третьих лиц, таких как задержки рейсов, дорожные аварии или заторы на границе, будет координироваться платформой, но без дополнительной компенсации.

II. Правила бронирования (краткий обзор)
Ключевой момент | Сборы и описание
--- | ---
Крайний срок бронирования | Трансфер из/в аэропорт ≥ 6 ч; Аренда автомобиля/индивидуальный заказ ≥ 24 ч (в пиковый сезон рекомендуется бронировать за 48 ч)
Назначение водителя | Автомобиль блокируется, и имя водителя, номерной знак, модель и цвет автомобиля отправляются в течение ≤ 30 мин после оплаты
Бесплатное время ожидания | Встреча в аэропорту: 90 мин (с момента посадки рейса); Проводы в аэропорт: 30 мин (с момента прибытия водителя)
Плата за превышение времени | После бесплатного времени ожидания, RM 20–60 за каждые 30 мин в зависимости от типа автомобиля
Бесплатная отмена | Полный возврат при отмене за ≥ 24 ч до отправления
Частичный возврат | При отмене за 6–24 ч до: штраф 20% + понесенные расходы на холостой пробег
Не подлежит возврату | При отмене < 6 ч, неявке пассажира или изменении пункта назначения на месте > 20 км
Изменение заказа | Самостоятельно в приложении за ≥ 24 ч до; через службу поддержки < 24 ч, с комиссией 10% и разницей в цене за обновление автомобиля

Нормы провоза багажа
4-местный хэтчбек (Myvi, Iriz): 2-3 человека + ≤ 2×24″ чемодана
5-местный седан (Saga, Bezza, Vios): 3-4 человека + ≤ 2×28″ или 3-4 человека + ≤ 3×24″
Удлиненный (Ertiga, Toyota Collora Cross): 4 человека + ≤ 3×24″ + 1×28″ или 4-5 человек + ≤ 2×20″
7-местный внедорожник (Alza, Avanza): 5 человек + ≤ 3×20″ или 4 человека + ≤ 4×24″
7-местный минивэн (New Alza, Aruz): 5 человек + ≤ 3×20″ или 4 человека + ≤ 4×24″
7-местный стандарт (Toyota Fortuner, Exora, Innova): 5 человек + ≤ 4×20″ или 4 человека + ≤ 5×24″
Люкс-минивэн (Serena, Voxy): 6 человек + ≤ 4×20″ или 4 человека + ≤ 5×24″
10-местный (Staria, Starex): 8-10 человек + ≤ 4×20″ или 7 человек + ≤ 7×24″
Премиум (Toyota Alphard, Vellfire): 5-6 человек + ≤ 4×20″ или 4 человека + ≤ 6×24″
18-местный фургон (Placer X): 16 человек или 12 человек + ≤ 12×24″

Детское кресло: Дополнительно 30 ринггитов за кресло за поездку, при наличии по запросу на месте.
Услуги передачи данных: Физическая SIM-карта, 10 ринггитов в день, безлимитный высокоскоростной интернет с возможностью раздачи.

Полезные советы
Гарантия при задержке рейса: При задержке ≤ 2 ч водитель будет ждать автоматически. При задержке 2–4 ч предлагается одно бесплатное перебронирование. При задержке > 4 ч вы можете перебронировать или получить полный возврат.
Оставайтесь на связи: Пожалуйста, включите мобильные данные или подключитесь к Wi-Fi в аэропорту перед получением багажа, чтобы получать сообщения WhatsApp/SMS от водителя.
Контактная информация: Пожалуйста, убедитесь, что вы можете получать WhatsApp / электронную почту, включив мобильные данные или используя бесплатный Wi-Fi в аэропорту перед получением багажа.

III. Служба поддержки и жалобы
Поддержка в реальном времени: Круглосуточный онлайн-чат в «Центре помощи» приложения (среднее время ответа < 60 секунд).
Телефонная поддержка: +6016‑223 4711 (09:00–23:00 MYT); с 00:00–09:00 выберите опцию 9 для связи с дежурным менеджером.
Электронная почта: <EMAIL> (гарантированный ответ в течение 24 часов).
Срок подачи жалоб: Письменные жалобы могут быть поданы в течение 7 дней после завершения поездки. Платформа предоставит первоначальный ответ в течение 48 часов и заключение в течение 5 рабочих дней.

Уведомление об авторских правах: Этот документ является собственностью GoMyHire. Его нельзя копировать, делать скриншоты или использовать в несанкционированных коммерческих целях. Версия: QR‑TnC‑20250624‑RU‑1.2 © 2025 GoMyHire Sdn. Bhd. Все права защищены.`
};
// ------------------- Конец перевода -------------------

/**
 * 等待指定的元素出现在DOM中。
 * @param {string} selector - 要等待的元素的CSS选择器。
 * @param {number} timeout - 等待的毫秒数。
 * @returns {Promise<Element|null>} - 返回找到的元素或在超时后返回null。
 */
function waitForElement(selector, timeout) {
    return new Promise(resolve => {
        const interval = 100;
        const endTime = Date.now() + timeout;

        const timer = setInterval(() => {
            const element = document.querySelector(selector);
            if (element) {
                clearInterval(timer);
                resolve(element);
            } else if (Date.now() > endTime) {
                clearInterval(timer);
                resolve(null);
            }
        }, interval);
    });
}


/**
 * 模拟点击指定的元素。
 * @param {string} selector - 要点击的元素的CSS选择器。
 * @param {number} timeout - 等待元素的毫秒数。
 * @returns {Promise<void>}
 */
async function clickElement(selector, timeout) {
    const element = await waitForElement(selector, timeout);
    if (element) {
        element.click();
    } else {
        throw new Error(`元素 "${selector}" 在 ${timeout}ms 内未找到，无法点击。`);
    }
}


/**
 * 根据检测到的语言，自动从 translations 对象中查找并填充TNC内容。
 * @returns {Promise<void>}
 */
async function autoFillTncByLanguage() {
    AutoTNCLogger.log('DEBUG', '开始尝试根据语言自动填充 TNC 内容');
    
    // 使用正确的ID选择器
    const langSelect = await waitForElement('#qrCodeTranslate_lang', 500);
    const descTextarea = document.getElementById('qrCodeTranslate_description');
    const remarkTextarea = document.getElementById('qrCodeTranslate_remark');

    if (!langSelect || !descTextarea || !remarkTextarea) {
        AutoTNCLogger.log('WARNING', '翻译浮窗的输入元素未完全加载，无法自动填充TNC内容', {
            langSelect: !!langSelect,
            descTextarea: !!descTextarea,
            remarkTextarea: !!remarkTextarea
        });
        return false;
    }

    const selectedLang = langSelect.value;
    const contentToFill = translations[selectedLang];

    AutoTNCLogger.log('INFO', `检测到当前语言: ${selectedLang}`, {
        hasTranslation: !!contentToFill,
        contentLength: contentToFill ? contentToFill.length : 0
    });

    if (contentToFill) {
        AutoTNCLogger.log('INFO', `开始填充 ${selectedLang} 语言内容`);
        
        // 记录原有内容长度
        const originalDescLength = descTextarea.value.length;
        const originalRemarkLength = remarkTextarea.value.length;
        
        // 清空现有内容后再填充
        descTextarea.value = '';
        remarkTextarea.value = '';
        
        // 延迟一点确保清空生效
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 分离条款内容：Description填入"条款与条件"部分，Remark填入"预订规则"部分
        // 多语言分割标识映射 - 使用更准确的分割方式
        const separators = {
            'zh-CN': {
                booking: '二、预订规则',
                customer: 'III. 客服与投诉'
            },
            'zh-TW': {
                booking: '二、預訂規則',
                customer: 'III. 客服與投訴'
            }, 
            'en': {
                booking: 'II. Booking Rules',
                customer: 'III. Customer Service'
            },
            'ms': {
                booking: 'II. Peraturan Tempahan',
                customer: 'III. Perkhidmatan Pelanggan'
            },
            'id': {
                booking: 'II. Aturan Pemesanan',
                customer: 'III. Layanan Pelanggan'
            },
            'ja': {
                booking: 'II. 予約規則',
                customer: 'III. カスタマーサービス'
            },
            'ko': {
                booking: 'II. 예약 규칙',
                customer: 'III. 고객 서비스'
            },
            'th': {
                booking: 'II. กฎการจอง',
                customer: 'III. ฝ่ายบริการลูกค้า'
            },
            'vi': {
                booking: 'II. Quy tắc đặt chỗ',
                customer: 'III. Dịch vụ khách hàng'
            },
            'ru': {
                booking: 'II. Правила бронирования',
                customer: 'III. Обслуживание клиентов'
            }
        };
        
        const langSeparators = separators[selectedLang] || separators['en'];
        AutoTNCLogger.log('DEBUG', `使用分割标识`, { 
            selectedLang, 
            booking: langSeparators.booking,
            customer: langSeparators.customer 
        });
        
        // 第一次分割：条款与条件 vs 其余内容
        const mainSections = contentToFill.split(langSeparators.booking);
        const termsConditions = mainSections[0].trim(); // 条款与条件部分（第一部分）
        
        // 第二次分割：预订规则 vs 客服部分
        let bookingRules = '';
        if (mainSections.length > 1) {
            const remainingContent = langSeparators.booking + mainSections[1];
            const bookingSections = remainingContent.split(langSeparators.customer);
            bookingRules = bookingSections[0].trim(); // 预订规则部分（不包含客服）
            
            AutoTNCLogger.log('DEBUG', '内容分离结果', {
                termsLength: termsConditions.length,
                bookingLength: bookingRules.length,
                termsPreview: termsConditions.substring(0, 100) + '...',
                bookingPreview: bookingRules.substring(0, 100) + '...'
            });
        } else {
            // 如果没有找到分割点，将全部内容放入条款与条件
            AutoTNCLogger.log('WARNING', '未找到预订规则分割点，全部内容归入条款与条件');
        }
        
        AutoTNCLogger.log('DEBUG', `内容分离结果`, {
            termsLength: termsConditions.length,
            rulesLength: bookingRules.length,
            language: selectedLang
        });
        
        // Description: 条款与条件
        descTextarea.value = termsConditions;
        // Remark: 预订规则  
        remarkTextarea.value = bookingRules;
        
        // 触发输入事件以确保UI正确更新
        descTextarea.dispatchEvent(new Event('input', { bubbles: true }));
        remarkTextarea.dispatchEvent(new Event('input', { bubbles: true }));
        descTextarea.dispatchEvent(new Event('change', { bubbles: true }));
        remarkTextarea.dispatchEvent(new Event('change', { bubbles: true }));
        
        AutoTNCLogger.log('SUCCESS', `${selectedLang} 语言内容填充完成`, {
            originalDescLength,
            originalRemarkLength,
            newDescLength: descTextarea.value.length,
            newRemarkLength: remarkTextarea.value.length,
            termsLength: termsConditions.length,
            rulesLength: bookingRules.length
        });
        
        AutoTNCLogger.stats.totalLanguagesProcessed++;
        return true;
    } else {
        AutoTNCLogger.log('INFO', `语言 ${selectedLang} 没有可用的翻译，跳过自动填充`);
        return false;
    }
}

async function preScanAndReport() {
    AutoTNCLogger.log('INFO', '🚀 开始预扫描，统计待处理任务...');
    const mainTranslateButtons = document.querySelectorAll('button[onclick*="qrCodeTranslate("]');
    if (mainTranslateButtons.length === 0) {
        AutoTNCLogger.log('WARNING', '页面上未找到翻译按钮，无法生成任务报告。');
        return 0;
    }

    const report = [];
    let totalLanguagesToProcess = 0;

    for (let i = 0; i < mainTranslateButtons.length; i++) {
        const button = mainTranslateButtons[i];
        const onclickAttr = button.getAttribute('onclick');

        // 1. 更稳健的ID提取方式
        const qrIdMatch = onclickAttr.match(/qrCodeTranslate\((.*?)\)/);
        const qrIdRaw = qrIdMatch ? qrIdMatch[1] : null;
        const qrId = qrIdRaw ? qrIdRaw.replace(/['"]/g, '') : `未知ID_${i + 1}`;
        
        let qrName = `QR ID: ${qrId}`; // 默认名称

        button.click();
        await new Promise(resolve => setTimeout(resolve, 1500)); // 等待弹窗

        const modal = await waitForElement('#qrCodeTranslateModal', 2000);
        if (modal) {
            // 2. 优先从弹窗标题获取名称
            const modalTitleEl = modal.querySelector('.modal-title');
            if (modalTitleEl && modalTitleEl.textContent) {
                // 假设标题是 "Translate for: QR_NAME" 或 "Translate (QR_NAME)"
                const modalTitle = modalTitleEl.textContent.trim();
                if (modalTitle.includes(':')) {
                    qrName = modalTitle.split(':')[1].trim();
                } else if (modalTitle.includes('(')) {
                    const nameMatch = modalTitle.match(/\((.*?)\)/);
                    if (nameMatch) qrName = nameMatch[1].trim();
                }
            } 
            // 3. 如果标题中没有，则尝试从表格行获取（备用方案）
            if (qrName.startsWith('QR ID:')) {
                const tableRow = button.closest('tr');
                if (tableRow) {
                    const nameCell = tableRow.querySelector('td:nth-child(2)'); // 假设名称在第二列
                    if (nameCell) {
                        qrName = nameCell.textContent.trim();
                    }
                }
            }

            const editButtons = modal.querySelectorAll('#qrCodeTranslateTable tbody button[onclick*="qrCodeTranslateEditModalOpen(\'edit\',"]');
            const languagesToProcess = editButtons.length;
            totalLanguagesToProcess += languagesToProcess;
            
            report.push({
                '序号': i + 1,
                'QR码名称': qrName,
                '待处理语言数': `${languagesToProcess} 种`,
            });

            // 关闭模态框
            const closeButton = modal.querySelector('.modal-footer button.btn-secondary, .modal-header .close');
            if (closeButton) {
                closeButton.click();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        } else {
            report.push({
                '序号': i + 1,
                'QR码名称': qrName,
                '待处理语言数': '错误：无法打开浮窗',
            });
        }
    }

    console.log('📋 %cGoMyHire QR码翻译任务清单', 'font-weight:bold; font-size: 16px; color: #1E90FF;');
    console.table(report);
    AutoTNCLogger.log('INFO', `📊 任务统计：总共 ${mainTranslateButtons.length} 个QR码，待处理 ${totalLanguagesToProcess} 个语言翻译。`);
    
    return totalLanguagesToProcess;
}

// 自动化点击流程函数
async function automateTranslationFlow() {
        // 首先进行预扫描并生成报告
        await preScanAndReport();
        
        AutoTNCLogger.log('INFO', '✅ 预扫描报告完成，5秒后将开始正式执行自动化翻译流程...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒，让用户查看报告

        AutoTNCLogger.startSession();
        
        // 1. 查找页面上所有的 "Translate" 按钮
        const mainTranslateButtons = document.querySelectorAll('button[onclick*="qrCodeTranslate("]');
        AutoTNCLogger.log('INFO', `正式执行：发现 ${mainTranslateButtons.length} 个QR码翻译按钮`);
        
        if (mainTranslateButtons.length === 0) {
            AutoTNCLogger.log('WARNING', '未找到主翻译按钮 (qrCodeTranslate)，流程终止。');
            AutoTNCLogger.endSession();
            return;
        }

        for (let qrIndex = 0; qrIndex < mainTranslateButtons.length; qrIndex++) {
            const mainButton = mainTranslateButtons[qrIndex];
            AutoTNCLogger.log('INFO', `开始处理第 ${qrIndex + 1}/${mainTranslateButtons.length} 个QR码`);
            
            // 尝试从按钮获取QR码信息
            const onclickAttr = mainButton.getAttribute('onclick');
            const qrIdMatch = onclickAttr.match(/qrCodeTranslate\((\d+)\)/);
            const qrId = qrIdMatch ? qrIdMatch[1] : 'unknown';
            
            AutoTNCLogger.log('DEBUG', `点击QR码翻译按钮`, { qrId, onclickAttr });
            
            mainButton.click();
            await new Promise(resolve => setTimeout(resolve, 1500)); // 等待主翻译浮窗打开

            const qrCodeTranslateModal = await waitForElement('#qrCodeTranslateModal', 2000);
            if (!qrCodeTranslateModal) {
                AutoTNCLogger.log('ERROR', `QR码 ${qrId} 的主翻译浮窗未打开，跳过`);
                continue;
            }

            AutoTNCLogger.log('SUCCESS', `QR码 ${qrId} 的主翻译浮窗已打开`);

            // 2. 在主翻译浮窗中遍历所有语言的 "Edit" 按钮
            const editButtonsInMainModal = qrCodeTranslateModal.querySelectorAll('#qrCodeTranslateTable tbody button[onclick*="qrCodeTranslateEditModalOpen(\'edit\',"]');
            
            AutoTNCLogger.log('INFO', `QR码 ${qrId} 发现 ${editButtonsInMainModal.length} 个可编辑的语言`);

            if (editButtonsInMainModal.length > 0) {
                 // 遍历所有语言的编辑按钮
                for (let langIndex = 0; langIndex < editButtonsInMainModal.length; langIndex++) {
                    const editButton = editButtonsInMainModal[langIndex];
                    
                    // 尝试从按钮获取语言信息
                    const editOnclick = editButton.getAttribute('onclick');
                    const langMatch = editOnclick.match(/qrCodeTranslateEditModalOpen\('edit',\s*(\d+),\s*'([^']+)'/);
                    const langCode = langMatch ? langMatch[2] : 'unknown';
                    
                    AutoTNCLogger.log('INFO', `处理第 ${langIndex + 1}/${editButtonsInMainModal.length} 个语言: ${langCode}`);
                    
                    editButton.click();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待二级翻译编辑浮窗打开

                    const qrCodeTranslateEditModal = await waitForElement('#qrCodeTranslateEditModal', 2000);
                    if (!qrCodeTranslateEditModal) {
                        AutoTNCLogger.log('ERROR', `语言 ${langCode} 的翻译编辑浮窗未打开，跳过`);
                        continue;
                    }
                    
                    // 自动填充、保存并关闭
                    await autoFillAndSaveAndCloseEditModal(qrId, langCode); 
                }
            } else {
                AutoTNCLogger.log('INFO', `QR码 ${qrId} 没有现有翻译可编辑，此功能当前不处理'Add Translate'`);
            }

            // 关闭主翻译浮窗
            try {
                AutoTNCLogger.log('DEBUG', `关闭QR码 ${qrId} 的主翻译浮窗`);
                await clickElement('#qrCodeTranslateModal .modal-footer button.btn-secondary', 1000); // 点击 Cancel 按钮
                await new Promise(resolve => setTimeout(resolve, 500)); // 等待浮窗关闭
                AutoTNCLogger.log('SUCCESS', `QR码 ${qrId} 的主翻译浮窗已关闭`);
            } catch (closeMainError) {
                AutoTNCLogger.log('ERROR', `关闭QR码 ${qrId} 主翻译浮窗失败`, closeMainError);
            }
            
            AutoTNCLogger.stats.totalQRProcessed++;
        }
        
        AutoTNCLogger.endSession();
}

// 辅助函数：填充、保存并关闭二级翻译编辑浮窗
    async function autoFillAndSaveAndCloseEditModal(qrId = 'unknown', langCode = 'unknown') {
        AutoTNCLogger.log('INFO', `开始处理QR码 ${qrId} 的 ${langCode} 语言翻译`);
        
        // 步骤1: 填充内容
        const fillResult = await autoFillTncByLanguage();
        
        if (!fillResult) {
            AutoTNCLogger.log('WARNING', `QR码 ${qrId} 的 ${langCode} 语言内容填充失败，但继续尝试保存`);
        }

        // 步骤2: 保存
        let saveSuccess = false;
        try {
            AutoTNCLogger.log('DEBUG', `QR码 ${qrId} ${langCode} 语言开始保存操作`);
            
            // 多种方式尝试点击submit按钮
            let submitSuccess = false;
            
            // 方法1: 尝试直接调用函数
            try {
                if (typeof submit_qrCodeTranslate_form === 'function') {
                    AutoTNCLogger.log('DEBUG', '尝试直接调用 submit_qrCodeTranslate_form 函数');
                    submit_qrCodeTranslate_form();
                    submitSuccess = true;
                }
            } catch (e) {
                AutoTNCLogger.log('DEBUG', '直接调用函数失败，尝试点击按钮', e.message);
            }
            
            // 方法2: 如果直接调用失败，尝试点击按钮
            if (!submitSuccess) {
                try {
                    const submitBtn = document.querySelector('.modal-footer .btn-primary[onclick*="submit_qrCodeTranslate_form"]');
                    if (submitBtn) {
                        AutoTNCLogger.log('DEBUG', '找到submit按钮，尝试点击');
                        submitBtn.click();
                        submitSuccess = true;
                    } else {
                        AutoTNCLogger.log('ERROR', '未找到submit按钮');
                    }
                } catch (e) {
                    AutoTNCLogger.log('ERROR', '点击submit按钮失败', e.message);
                }
            }
            
            // 方法3: 如果前两种都失败，尝试模拟事件
            if (!submitSuccess) {
                try {
                    const submitBtn = document.querySelector('.modal-footer .btn-primary');
                    if (submitBtn) {
                        AutoTNCLogger.log('DEBUG', '尝试模拟点击事件');
                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true
                        });
                        submitBtn.dispatchEvent(clickEvent);
                        submitSuccess = true;
                    }
                } catch (e) {
                    AutoTNCLogger.log('ERROR', '模拟点击事件失败', e.message);
                }
            }
            
            if (!submitSuccess) {
                throw new Error('所有submit方法都失败了');
            }
            
            AutoTNCLogger.log('SUCCESS', 'Submit按钮点击成功');
            await new Promise(resolve => setTimeout(resolve, 1500)); // 等待保存操作和可能的SweetAlert

            // 处理SweetAlert
            const swalModal = await waitForElement('.swal-modal', 2000);
            if (swalModal) {
                AutoTNCLogger.log('DEBUG', `检测到 SweetAlert 弹窗，准备确认保存`);
                const confirmButton = swalModal.querySelector('button.swal-button--confirm');
                if (confirmButton) {
                    confirmButton.click();
                    await new Promise(resolve => setTimeout(resolve, 500)); // 等待SweetAlert关闭
                    AutoTNCLogger.log('SUCCESS', `QR码 ${qrId} 的 ${langCode} 语言翻译保存成功`);
                    saveSuccess = true;
                    AutoTNCLogger.stats.successfulTranslations++;
                } else {
                    AutoTNCLogger.log('ERROR', `SweetAlert 确认按钮未找到`);
                }
            } else {
                // 没有SweetAlert可能表示保存成功或有其他响应
                AutoTNCLogger.log('INFO', `QR码 ${qrId} 的 ${langCode} 语言保存完成（无SweetAlert弹窗）`);
                saveSuccess = true;
                AutoTNCLogger.stats.successfulTranslations++;
            }
        } catch (saveError) {
            AutoTNCLogger.log('ERROR', `QR码 ${qrId} 的 ${langCode} 语言保存失败`, saveError);
            AutoTNCLogger.stats.failedTranslations++;
        }

        // 步骤3: 关闭编辑浮窗 (无论保存成功与否都尝试关闭)
        try {
            AutoTNCLogger.log('DEBUG', `关闭QR码 ${qrId} ${langCode} 语言的编辑浮窗`);
            // 使用更精确的选择器，避免关闭其他模态框
            const cancelButton = await waitForElement('#qrCodeTranslateEditModal .modal-footer button[onclick*="close_modal(\'qrCodeTranslateEditModal\')"]', 1000);
            if (cancelButton) {
                cancelButton.click();
                AutoTNCLogger.log('SUCCESS', `QR码 ${qrId} ${langCode} 语言编辑浮窗已关闭`);
            } else {
                // 作为备用方案
                await clickElement('#qrCodeTranslateEditModal .close', 1000);
                AutoTNCLogger.log('SUCCESS', `QR码 ${qrId} ${langCode} 语言编辑浮窗已关闭（备用方案）`);
            }
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待浮窗关闭
        } catch (closeEditError) {
            AutoTNCLogger.log('ERROR', `关闭QR码 ${qrId} ${langCode} 语言编辑浮窗失败`, closeEditError);
        }
        
        return saveSuccess;
    }

    // 页面加载完成后立即执行自动化流程
    document.addEventListener('DOMContentLoaded', () => {
        automateTranslationFlow();
    });

    // 脚本加载完成提示
    console.log("✅ GoMyHire QR码翻译自动化脚本已加载完成！");

// 暴露便捷的控制台命令
window.AutoTNC = {
    // 开始自动化流程
    start: function() {
        AutoTNCLogger.log('INFO', '手动触发自动化翻译流程');
        const event = new Event('DOMContentLoaded');
        document.dispatchEvent(event);
    },
    
    // 查看日志报告
    getReport: function() {
        const report = AutoTNCLogger.getReport();
        console.table(report.summary);
        console.log('📋 详细日志:', report.logs);
        if (report.errorDetails.length > 0) {
            console.log('❌ 错误详情:', report.errorDetails);
        }
        return report;
    },
    
    // 查看最近的日志
    getLogs: function(count = 10) {
        const recentLogs = AutoTNCLogger.logs.slice(-count);
        console.table(recentLogs);
        return recentLogs;
    },
    
    // 清空日志
    clearLogs: function() {
        AutoTNCLogger.clearLogs();
    },
    
    // 查看统计信息
    getStats: function() {
        console.table(AutoTNCLogger.stats);
        return AutoTNCLogger.stats;
    },
    
    // 查看可用的翻译语言
    getAvailableLanguages: function() {
        const languages = Object.keys(translations);
        console.log('🌐 可用翻译语言:', languages);
        return languages;
    },
    
    // 手动填充单个语言（调试用）
    fillLanguage: async function(langCode) {
        if (!translations[langCode]) {
            console.log(`❌ 语言 ${langCode} 没有可用翻译`);
            return false;
        }
        
        const descTextarea = document.getElementById('qrCodeTranslate_description');
        const remarkTextarea = document.getElementById('qrCodeTranslate_remark');
        
        if (descTextarea && remarkTextarea) {
            const contentToFill = translations[langCode];
            
            // 清空现有内容
            descTextarea.value = '';
            remarkTextarea.value = '';
            
            // 分离内容 - 多语言支持，使用与主函数相同的逻辑
            const separators = {
                'zh-CN': {
                    booking: '二、预订规则',
                    customer: 'III. 客服与投诉'
                },
                'zh-TW': {
                    booking: '二、預訂規則',
                    customer: 'III. 客服與投訴'
                }, 
                'en': {
                    booking: 'II. Booking Rules',
                    customer: 'III. Customer Service'
                },
                'ms': {
                    booking: 'II. Peraturan Tempahan',
                    customer: 'III. Perkhidmatan Pelanggan'
                },
                'id': {
                    booking: 'II. Aturan Pemesanan',
                    customer: 'III. Layanan Pelanggan'
                },
                'ja': {
                    booking: 'II. 予約規則',
                    customer: 'III. カスタマーサービス'
                },
                'ko': {
                    booking: 'II. 예약 규칙',
                    customer: 'III. 고객 서비스'
                },
                'th': {
                    booking: 'II. กฎการจอง',
                    customer: 'III. ฝ่ายบริการลูกค้า'
                },
                'vi': {
                    booking: 'II. Quy tắc đặt chỗ',
                    customer: 'III. Dịch vụ khách hàng'
                },
                'ru': {
                    booking: 'II. Правила бронирования',
                    customer: 'III. Обслуживание клиентов'
                }
            };
            
            const langSeparators = separators[langCode] || separators['en'];
            
            // 第一次分割：条款与条件 vs 其余内容
            const mainSections = contentToFill.split(langSeparators.booking);
            const termsConditions = mainSections[0].trim();
            
            // 第二次分割：预订规则 vs 客服部分
            let bookingRules = '';
            if (mainSections.length > 1) {
                const remainingContent = langSeparators.booking + mainSections[1];
                const bookingSections = remainingContent.split(langSeparators.customer);
                bookingRules = bookingSections[0].trim();
            }
            
            // 填充分离的内容
            descTextarea.value = termsConditions;
            remarkTextarea.value = bookingRules;
            
            descTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            remarkTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            descTextarea.dispatchEvent(new Event('change', { bubbles: true }));
            remarkTextarea.dispatchEvent(new Event('change', { bubbles: true }));
            
            console.log(`✅ 已手动填充 ${langCode} 语言内容`);
            console.log(`📝 条款与条件长度: ${termsConditions.length}`);
            console.log(`📝 预订规则长度: ${bookingRules.length}`);
            return true;
        } else {
            console.log('❌ 未找到翻译输入框');
            return false;
        }
    }
};

// 显示使用说明
console.log(`
🎯 使用说明：
• AutoTNC.start() - 开始/重新开始自动化流程
• AutoTNC.getReport() - 查看完整报告
• AutoTNC.getLogs(10) - 查看最近10条日志
• AutoTNC.getStats() - 查看统计信息
• AutoTNC.clearLogs() - 清空日志
• AutoTNC.getAvailableLanguages() - 查看可用语言
• AutoTNC.fillLanguage('en') - 手动填充指定语言内容
`);

// 全局日志收集器
window.AutoTNCLogger = {
    logs: [],
    stats: {
        startTime: null,
        endTime: null,
        totalQRProcessed: 0,
        totalLanguagesProcessed: 0,
        successfulTranslations: 0,
        failedTranslations: 0,
        errors: []
    },
    
    log: function(level, message, data = null) {
        const timestamp = new Date().toLocaleString();
        const logEntry = {
            timestamp,
            level,
            message,
            data
        };
        this.logs.push(logEntry);
        
        // 同时输出到控制台
        const emoji = {
            'INFO': 'ℹ️',
            'SUCCESS': '✅',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'DEBUG': '🔍'
        };
        
        console.log(`${emoji[level] || '📝'} [${timestamp}] ${message}`, data || '');
        
        // 如果是错误，记录到统计中
        if (level === 'ERROR') {
            this.stats.errors.push(logEntry);
        }
    },
    
    startSession: function() {
        this.stats.startTime = new Date();
        this.log('INFO', '=== 开始新的翻译会话 ===');
    },
    
    endSession: function() {
        this.stats.endTime = new Date();
        const duration = (this.stats.endTime - this.stats.startTime) / 1000;
        this.log('INFO', '=== 翻译会话结束 ===');
        this.log('INFO', `会话总耗时: ${duration.toFixed(2)} 秒`);
        this.log('INFO', `处理的QR码数量: ${this.stats.totalQRProcessed}`);
        this.log('INFO', `处理的语言数量: ${this.stats.totalLanguagesProcessed}`);
        this.log('INFO', `成功翻译: ${this.stats.successfulTranslations}`);
        this.log('INFO', `失败翻译: ${this.stats.failedTranslations}`);
        if (this.stats.errors.length > 0) {
            this.log('WARNING', `遇到错误数量: ${this.stats.errors.length}`);
        }
    },
    
    getReport: function() {
        return {
            summary: this.stats,
            logs: this.logs,
            errorDetails: this.stats.errors
        };
    },
    
    clearLogs: function() {
        this.logs = [];
        this.stats = {
            startTime: null,
            endTime: null,
            totalQRProcessed: 0,
            totalLanguagesProcessed: 0,
            successfulTranslations: 0,
            failedTranslations: 0,
            errors: []
        };
        this.log('INFO', '日志已清空');
    }
};

// 立即执行自动化流程（脚本注入后自动开始）
(async function() {
    console.log("🚀 脚本已注入，开始自动化翻译流程...");
    
    // 检查页面是否已加载完成
    if (document.readyState === 'loading') {
        await new Promise(resolve => {
            document.addEventListener('DOMContentLoaded', resolve);
        });
    }
    
    // 等待页面元素完全准备就绪
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 开始自动化流程
    try {
        await automateTranslationFlow();
    } catch (error) {
        console.error("❌ 自动化流程执行失败:", error);
    }
})();