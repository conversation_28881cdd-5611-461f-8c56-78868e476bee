# GoMyHire QR Code 全量重新审计 - AQS-SOP v3.0

**最后更新**: 2024-07-27
**负责人**: AI Assistant
**SOP**: [SOP_QR_Code_Audit_Workflow.md](SOP_QR_Code_Audit_Workflow.md)

---

## 审计目标

根据 `AQS-SOP v3.0` 流程，对所有QR码项目进行彻底的、严格的重新审查，以确保其下属所有子项目的ID、名称及翻译状态的100%准确性。

---

## 审计进度跟踪 (Auditing Progress)

| 二维码ID (qrcode_id) | 项目名称 (Project Name) | 审计状态 (Audit Status) |
| :--- | :--- | :--- |
| 81 | KTMB | ✅ 已审计 & 已验证 (2024-07-27) |
| 80 | Xiaoxuan QR | ✅ 已审计 & 已验证 (2024-07-27) |
| 79 | GoMyHire HK Fb Ads | ⏳ 待审计 (Pending) |
| 78 | GoMyHire FB Ads RM68 Airport Dropoff | ⏳ 待审计 (Pending) |
| 77 | GMH Driver App | ⏳ 待审计 (Pending) |
| 74 | Joshua Transport | ⏳ 待审计 (Pending) |
| 73 | Cynthia QR | ⏳ 待审计 (Pending) |
| 72 | Want To Eat | ⏳ 待审计 (Pending) |
| 71 | SMW Walkin | ⏳ 待审计 (Pending) |
| 69 | Lewis | ⏳ 待审计 (Pending) |
| 68 | Vivian QR | ⏳ 待审计 (Pending) |
| 67 | Venus QR | ⏳ 待审计 (Pending) |
| 66 | Karen QR | ⏳ 待审计 (Pending) |
| 65 | Qijun QR | ⏳ 待审计 (Pending) |
| 64 | Ms Yong QR | ⏳ 待审计 (Pending) |
| 63 | Jyeo QR | ⏳ 待审计 (Pending) |
| 62 | SweeQing QR | ⏳ 待审计 (Pending) |
| 61 | Wendy QR | ⏳ 待审计 (Pending) |
| 60 | Agent Victor | ⏳ 待审计 (Pending) |
| 59 | ONE18 Boutique Hotel | ⏳ 待审计 (Pending) |
| 57 | ONE18 Boutique Hotel | ⏳ 待审计 (Pending) |
| 56 | GoMyHire Billboard | ⏳ 待审计 (Pending) |
| 55 | GoMyHire CS/Sales | ⏳ 待审计 (Pending) |
| 53 | For Syn | ⏳ 待审计 (Pending) |
| 52 | CEO chaffer premium | ⏳ 待审计 (Pending) |
| 51 | ReSklils | ⏳ 待审计 (Pending) |
| 49 | GoMyHire Webpage | ⏳ 待审计 (Pending) |
| 47 | KLIA Berjaya Hill | ⏳ 待审计 (Pending) |
| 46 | Bintang Collectionz Hotel | ⏳ 待审计 (Pending) |
| 45 | 789 Genting - KL<=> Genting | ⏳ 待审计 (Pending) |
| 43 | Kuala Lumput City Tour ( 4 / 8 Hours) | ⏳ 待审计 (Pending) |
| 42 | GoMyHire - Klia <=> Genting | ⏳ 待审计 (Pending) |
| 41 | The Pearl Kuala Lumpur Hotel | ⏳ 待审计 (Pending) |
| 40 | The Maple Suite - Bukit Bintang | ⏳ 待审计 (Pending) |
| 38 | Jcy Member | ⏳ 待审计 (Pending) |
| 36 | PS Badminton Team & Family | ⏳ 待审计 (Pending) |
| 35 | PS Member | ⏳ 待审计 (Pending) |
| 34 | The Maple Suite - Bukit Bintang | ⏳ 待审计 (Pending) |
| 33 | Stan | ⏳ 待审计 (Pending) |
| 32 | GoMyhire Airport transfer (Within 30KM of KL City Centre） | ⏳ 待审计 (Pending) |
| 30 | WT by yap | ⏳ 待审计 (Pending) |
| 29 | aakl | ⏳ 待审计 (Pending) |
| 27 | BNI Member | ⏳ 待审计 (Pending) |
| 26 | wilson | ⏳ 待审计 (Pending) |
| 25 | jcyap | ⏳ 待审计 (Pending) |
| 23 | Chong Dealer | ⏳ 待审计 (Pending) |
| 22 | testing | ⏳ 待审计 (Pending) |
| 21 | Amber Cove Premier Suites Melaka by MAPLEHOME | ⏳ 待审计 (Pending) |
| 20 | The Apple Premier Suites Melaka by MAPLEHOME | ⏳ 待审计 (Pending) |
| 19 | Geo38 Premier Suites Genting Highlands by MAPLEHOME | ⏳ 待审计 (Pending) |
| 18 | Chambers Premier Suites Kuala Lumpur by MAPLEHOME | ⏳ 待审计 (Pending) |
| 14 | KKDAY | ⏳ 待审计 (Pending) |
| 13 | Swiss Garden Residence Kuala Lumpur by MAPLEHOME | ⏳ 待审计 (Pending) |
| 3 | The Robertson Kuala Lumpur by MAPLEHOME | ⏳ 待审计 (Pending) |
| 2 | UCSI - Cheras | ⏳ 待审计 (Pending) |

---

## 严格审查流程 (SOP v2.0)

**目标**: 对每一个 `qrcode` 进行无差错的数据提取和状态审查。

| 步骤 | 操作 | 验证方法 | 关键指令 |
| :--- | :--- | :--- | :--- |
| **1** | **清空环境** | N/A | 在审查新`qrcode`前，连续按`Escape`键3次，确保关闭所有可能存在的浮窗。 |
| **2** | **打开主编辑窗** | 检查`#qrCodeMainModalTitle` | 打开目标`qrcode ID`的编辑窗口。 | 使用CSSselector工具操作click按钮 
| **3** | **验证主窗口** | **读取窗口标题和隐藏ID字段 (`#edit_qrCode_main_id`)，必须与目标`qrcode`完全匹配。** | **如不匹配，则流程失败，立即停止并报告错误。** |
| **4** | **提取子项目列表** | N/A | 验证成功后，提取所有`subqr ID`和名称。 |
| **5** | **循环审查子项目** | N/A | 对每一个`subqr ID`执行以下6-9步。 |
| **6** | **打开翻译窗口** | 检查`#qrCodeSubTranslateModal`是否出现 | 点击目标`subqr ID`的`Translate`按钮。 |
| **7** | **验证翻译窗口** | **读取隐藏的`sub_id`字段 (`#qrCodeSubTranslateModal input[id*='sub_id']`)，必须与目标`subqr`完全匹配。** | **如不匹配，则流程失败，立即停止并报告错误。** |
| **8** | **记录翻译状态** | 读取`#qrCodeSubTranslateTable_info` | 验证成功后，记录该`subqr`的翻译状态 (`X/10`)。 |
| **9** | **关闭翻译窗口** | N/A | 按`Escape`键关闭当前`subqr`的翻译窗口。 |
| **10** | **关闭主窗口** | N/A | 完成所有`subqr`审查后，按`Escape`键关闭主编辑窗口。 |

---

## 审查结果记录

**(此部分将根据新的、严格的审查结果进行填写)**

**已审查进度**: 8/55 QR码

---

## 🚨 非标准子项目清单 (待处理)

**所有项目均已处理完毕，此列表已清空。**

| 主项目 (Parent QR Code) | 非标准子项目名称 (Non-Standard Sub-QR Name) |
| :--- | :--- |
